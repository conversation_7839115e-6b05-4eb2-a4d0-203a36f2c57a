"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx":
/*!*************************************************!*\
  !*** ./app/logs/components/LogDetailsModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogDetailsModal: function() { return /* binding */ LogDetailsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDatabase,FiEye,FiEyeOff,FiFileText,FiTag,FiUser,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nfunction LogDetailsModal(param) {\n    let { isOpen, log, onClose, userName } = param;\n    _s();\n    // State for toggling old/new data visibility\n    const [showOldData, setShowOldData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showNewData, setShowNewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Handle escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                e.preventDefault();\n                onClose();\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Handle clicking outside modal to close\n    const handleOverlayClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    // Format date for display\n    const formatDate = (date)=>{\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        return dateObj.toLocaleString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    // Get operation color based on operation type\n    const getOperationColor = (operation)=>{\n        const op = operation.toLowerCase();\n        if (op.includes(\"create\") || op.includes(\"add\")) {\n            return \"bg-green-100 text-green-800 border-green-200\";\n        } else if (op.includes(\"update\") || op.includes(\"edit\") || op.includes(\"modify\")) {\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        } else if (op.includes(\"delete\") || op.includes(\"remove\")) {\n            return \"bg-red-100 text-red-800 border-red-200\";\n        } else if (op.includes(\"cancel\") || op.includes(\"reject\")) {\n            return \"bg-orange-100 text-orange-800 border-orange-200\";\n        } else if (op.includes(\"approve\") || op.includes(\"complete\")) {\n            return \"bg-purple-100 text-purple-800 border-purple-200\";\n        } else if (op.includes(\"transfer\") || op.includes(\"move\")) {\n            return \"bg-indigo-100 text-indigo-800 border-indigo-200\";\n        } else if (op.includes(\"adjust\") || op.includes(\"modify\")) {\n            return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n        } else {\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    // Format JSON data for display\n    const formatJsonData = (data)=>{\n        try {\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            return \"Invalid JSON data\";\n        }\n    };\n    // Check if the log data contains delta changes\n    const hasDeltaChanges = (data)=>{\n        return data && data.changes && typeof data.changes === \"object\";\n    };\n    // Render overlapped before/after values with toggle controls\n    const renderOverlappedValues = (before, after)=>{\n        const beforeStr = before === null ? \"null\" : typeof before === \"object\" ? JSON.stringify(before, null, 2) : String(before);\n        const afterStr = after === null ? \"null\" : typeof after === \"object\" ? JSON.stringify(after, null, 2) : String(after);\n        const beforeLines = beforeStr.split(\"\\n\");\n        const afterLines = afterStr.split(\"\\n\");\n        const maxLines = Math.max(beforeLines.length, afterLines.length);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-100 px-3 py-2 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-red-200 border border-red-400 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Before\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-green-200 border border-green-400 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"After\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowOldData(!showOldData),\n                                        className: \"flex items-center gap-1 px-2 py-1 text-xs rounded transition-colors \".concat(showOldData ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-gray-200 text-gray-500 hover:bg-gray-300\"),\n                                        title: showOldData ? \"Hide old data\" : \"Show old data\",\n                                        children: [\n                                            showOldData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiEye, {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiEyeOff, {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 64\n                                            }, this),\n                                            \"Old\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowNewData(!showNewData),\n                                        className: \"flex items-center gap-1 px-2 py-1 text-xs rounded transition-colors \".concat(showNewData ? \"bg-green-100 text-green-700 hover:bg-green-200\" : \"bg-gray-200 text-gray-500 hover:bg-gray-300\"),\n                                        title: showNewData ? \"Hide new data\" : \"Show new data\",\n                                        children: [\n                                            showNewData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiEye, {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 32\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiEyeOff, {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 64\n                                            }, this),\n                                            \"New\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 max-h-64 overflow-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-mono text-sm leading-relaxed\",\n                        children: Array.from({\n                            length: maxLines\n                        }, (_, i)=>{\n                            const beforeLine = beforeLines[i] || \"\";\n                            const afterLine = afterLines[i] || \"\";\n                            const isChanged = beforeLine !== afterLine;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative min-h-[1.5rem]\",\n                                children: isChanged ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        beforeLine && showOldData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-600 bg-red-50 px-1 line-through opacity-75\",\n                                            children: beforeLine\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 25\n                                        }, this),\n                                        afterLine && showNewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-700 bg-green-50 px-1 font-medium\",\n                                            children: afterLine\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /* Unchanged line - show if either toggle is on */ (showOldData || showNewData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 px-1\",\n                                    children: beforeLine\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 23\n                                }, this)\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    };\n    // Render delta changes in a user-friendly format\n    const renderDeltaChanges = (changes)=>{\n        const changeEntries = Object.entries(changes);\n        if (changeEntries.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"No changes detected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: changeEntries.map((param)=>{\n                let [field, change] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border border-gray-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"font-medium text-gray-900 mb-4 capitalize\",\n                            children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase())\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"\\uD83D\\uDCCB Before/After Comparison\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mb-3\",\n                                    children: \"Shows changes inline with strikethrough for removed content and highlighting for added content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this),\n                                renderOverlappedValues(change.before, change.after)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, field, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isOpen || !log) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        onClick: handleOverlayClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Log Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: \"View detailed information about this log entry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                            title: \"Close\",\n                            \"aria-label\": \"Close modal\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTag, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Operation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getOperationColor(log.operation)),\n                                                    children: log.operation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Entity\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: log.entity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUser, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: userName || \"Unknown User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: log.userUuid\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Timestamp\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDate(log.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                log.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiFileText, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 bg-gray-50 p-3 rounded-lg border\",\n                                            children: log.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: log.data && hasDeltaChanges(log.data) ? \"Changes\" : \"Additional Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                log.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: hasDeltaChanges(log.data) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                        children: [\n                                                            \"Field Changes (\",\n                                                            log.data.changeCount || 0,\n                                                            \" changes)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    renderDeltaChanges(log.data.changes)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, this),\n                                            Object.keys(log.data).some((key)=>![\n                                                    \"changes\",\n                                                    \"changedFields\",\n                                                    \"changeCount\"\n                                                ].includes(key)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Additional Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                            children: formatJsonData(Object.fromEntries(Object.entries(log.data).filter((param)=>{\n                                                                let [key] = param;\n                                                                return ![\n                                                                    \"changes\",\n                                                                    \"changedFields\",\n                                                                    \"changeCount\"\n                                                                ].includes(key);\n                                                            })))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"JSON Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                    children: formatJsonData(log.data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiEye_FiEyeOff_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                            className: \"w-12 h-12 text-gray-300 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"No additional data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Log ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                    children: log.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex-shrink-0 flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(LogDetailsModal, \"tf0jVYPlevYmUo5AW0nYjE1aztI=\");\n_c = LogDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"LogDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9sb2dzL2NvbXBvbmVudHMvTG9nRGV0YWlsc01vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW1EO0FBQ3NEO0FBVWxHLFNBQVNXLGdCQUFnQixLQUF3RDtRQUF4RCxFQUFFQyxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQXdCLEdBQXhEOztJQUM5Qiw2Q0FBNkM7SUFDN0MsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dCLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFDO0lBRS9DLG1DQUFtQztJQUNuQ0QsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNXLFFBQVE7UUFFYixNQUFNUSxnQkFBZ0IsQ0FBQ0M7WUFDckIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFVBQVU7Z0JBQ3RCRCxFQUFFRSxjQUFjO2dCQUNoQlQ7WUFDRjtRQUNGO1FBRUFVLE9BQU9DLGdCQUFnQixDQUFDLFdBQVdMO1FBQ25DLE9BQU8sSUFBTUksT0FBT0UsbUJBQW1CLENBQUMsV0FBV047SUFDckQsR0FBRztRQUFDUjtRQUFRRTtLQUFRO0lBRXBCLHlDQUF5QztJQUN6QyxNQUFNYSxxQkFBcUIsQ0FBQ047UUFDMUIsSUFBSUEsRUFBRU8sTUFBTSxLQUFLUCxFQUFFUSxhQUFhLEVBQUU7WUFDaENmO1FBQ0Y7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNZ0IsYUFBYSxDQUFDQztRQUNsQixNQUFNQyxVQUFVLE9BQU9ELFNBQVMsV0FBVyxJQUFJRSxLQUFLRixRQUFRQTtRQUM1RCxPQUFPQyxRQUFRRSxjQUFjLENBQUMsU0FBUztZQUNyQ0MsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFFBQVE7UUFDVjtJQUNGO0lBRUEsOENBQThDO0lBQzlDLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QixNQUFNQyxLQUFLRCxVQUFVRSxXQUFXO1FBQ2hDLElBQUlELEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsUUFBUTtZQUMvQyxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVdGLEdBQUdFLFFBQVEsQ0FBQyxXQUFXO1lBQ2hGLE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsV0FBVztZQUN6RCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVc7WUFDekQsT0FBTztRQUNULE9BQU8sSUFBSUYsR0FBR0UsUUFBUSxDQUFDLGNBQWNGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhO1lBQzVELE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxlQUFlRixHQUFHRSxRQUFRLENBQUMsU0FBUztZQUN6RCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVc7WUFDekQsT0FBTztRQUNULE9BQU87WUFDTCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLCtCQUErQjtJQUMvQixNQUFNQyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSTtZQUNGLE9BQU9DLEtBQUtDLFNBQVMsQ0FBQ0YsTUFBTSxNQUFNO1FBQ3BDLEVBQUUsT0FBT0csT0FBTztZQUNkLE9BQU87UUFDVDtJQUNGO0lBRUEsK0NBQStDO0lBQy9DLE1BQU1DLGtCQUFrQixDQUFDSjtRQUN2QixPQUFPQSxRQUFRQSxLQUFLSyxPQUFPLElBQUksT0FBT0wsS0FBS0ssT0FBTyxLQUFLO0lBQ3pEO0lBSUEsNkRBQTZEO0lBQzdELE1BQU1DLHlCQUF5QixDQUFDQyxRQUFhQztRQUMzQyxNQUFNQyxZQUFZRixXQUFXLE9BQU8sU0FDbkIsT0FBT0EsV0FBVyxXQUNsQk4sS0FBS0MsU0FBUyxDQUFDSyxRQUFRLE1BQU0sS0FDN0JHLE9BQU9IO1FBRXhCLE1BQU1JLFdBQVdILFVBQVUsT0FBTyxTQUNsQixPQUFPQSxVQUFVLFdBQ2pCUCxLQUFLQyxTQUFTLENBQUNNLE9BQU8sTUFBTSxLQUM1QkUsT0FBT0Y7UUFFdkIsTUFBTUksY0FBY0gsVUFBVUksS0FBSyxDQUFDO1FBQ3BDLE1BQU1DLGFBQWFILFNBQVNFLEtBQUssQ0FBQztRQUNsQyxNQUFNRSxXQUFXQyxLQUFLQyxHQUFHLENBQUNMLFlBQVlNLE1BQU0sRUFBRUosV0FBV0ksTUFBTTtRQUUvRCxxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUtELFdBQVU7OzBEQUNkLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzRDQUF5RDs7Ozs7OztrREFHMUUsOERBQUNDO3dDQUFLRCxXQUFVOzswREFDZCw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7Ozs0Q0FBNkQ7Ozs7Ozs7Ozs7Ozs7MENBTWhGLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUNDQyxTQUFTLElBQU1yRCxlQUFlLENBQUNEO3dDQUMvQm1ELFdBQVcsdUVBSVYsT0FIQ25ELGNBQ0ksNkNBQ0E7d0NBRU51RCxPQUFPdkQsY0FBYyxrQkFBa0I7OzRDQUV0Q0EsNEJBQWMsOERBQUNQLHlJQUFLQTtnREFBQzBELFdBQVU7Ozs7O3FFQUFlLDhEQUFDekQsNElBQVFBO2dEQUFDeUQsV0FBVTs7Ozs7OzRDQUFhOzs7Ozs7O2tEQUlsRiw4REFBQ0U7d0NBQ0NDLFNBQVMsSUFBTW5ELGVBQWUsQ0FBQ0Q7d0NBQy9CaUQsV0FBVyx1RUFJVixPQUhDakQsY0FDSSxtREFDQTt3Q0FFTnFELE9BQU9yRCxjQUFjLGtCQUFrQjs7NENBRXRDQSw0QkFBYyw4REFBQ1QseUlBQUtBO2dEQUFDMEQsV0FBVTs7Ozs7cUVBQWUsOERBQUN6RCw0SUFBUUE7Z0RBQUN5RCxXQUFVOzs7Ozs7NENBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFReEYsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWkssTUFBTUMsSUFBSSxDQUFDOzRCQUFFUixRQUFRSDt3QkFBUyxHQUFHLENBQUNZLEdBQUdDOzRCQUNwQyxNQUFNQyxhQUFhakIsV0FBVyxDQUFDZ0IsRUFBRSxJQUFJOzRCQUNyQyxNQUFNRSxZQUFZaEIsVUFBVSxDQUFDYyxFQUFFLElBQUk7NEJBQ25DLE1BQU1HLFlBQVlGLGVBQWVDOzRCQUVqQyxxQkFDRSw4REFBQ1g7Z0NBQVlDLFdBQVU7MENBQ3BCVywwQkFDQzs7d0NBRUdGLGNBQWM1RCw2QkFDYiw4REFBQ2tEOzRDQUFJQyxXQUFVO3NEQUNaUzs7Ozs7O3dDQUlKQyxhQUFhM0QsNkJBQ1osOERBQUNnRDs0Q0FBSUMsV0FBVTtzREFDWlU7Ozs7Ozs7bURBTVAsZ0RBRGdELEdBQy9DN0QsQ0FBQUEsZUFBZUUsV0FBVSxtQkFDeEIsOERBQUNnRDtvQ0FBSUMsV0FBVTs4Q0FDWlM7Ozs7OzsrQkFwQkNEOzs7Ozt3QkEwQmQ7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS1Y7SUFJQSxpREFBaUQ7SUFDakQsTUFBTUkscUJBQXFCLENBQUMzQjtRQUMxQixNQUFNNEIsZ0JBQWdCQyxPQUFPQyxPQUFPLENBQUM5QjtRQUVyQyxJQUFJNEIsY0FBY2YsTUFBTSxLQUFLLEdBQUc7WUFDOUIscUJBQ0UsOERBQUNDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDZ0I7b0JBQUVoQixXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7UUFHM0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTtzQkFDWmEsY0FBY0ksR0FBRyxDQUFDO29CQUFDLENBQUNDLE9BQU9DLE9BQU87Z0JBQ2pDLHFCQUNFLDhEQUFDcEI7b0JBQWdCQyxXQUFVOztzQ0FDekIsOERBQUNvQjs0QkFBR3BCLFdBQVU7c0NBQ1hrQixNQUFNRyxPQUFPLENBQUMsWUFBWSxPQUFPQSxPQUFPLENBQUMsTUFBTUMsQ0FBQUEsTUFBT0EsSUFBSUMsV0FBVzs7Ozs7O3NDQUl4RSw4REFBQ3hCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3dCO29DQUFNeEIsV0FBVTs4Q0FBMEM7Ozs7Ozs4Q0FHM0QsOERBQUNnQjtvQ0FBRWhCLFdBQVU7OENBQTZCOzs7Ozs7Z0NBR3pDZCx1QkFBdUJpQyxPQUFPaEMsTUFBTSxFQUFFZ0MsT0FBTy9CLEtBQUs7Ozs7Ozs7O21CQWI3QzhCOzs7OztZQWlCZDs7Ozs7O0lBR047SUFFQSxJQUFJLENBQUN6RSxVQUFVLENBQUNDLEtBQUssT0FBTztJQUU1QixxQkFDRSw4REFBQ3FEO1FBQUlDLFdBQVU7UUFBNkVHLFNBQVMzQztrQkFDbkcsNEVBQUN1QztZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUMwQjtvQ0FBR3pCLFdBQVU7OENBQWtDOzs7Ozs7OENBR2hELDhEQUFDZ0I7b0NBQUVoQixXQUFVOzhDQUE2Qjs7Ozs7Ozs7Ozs7O3NDQUk1Qyw4REFBQ0U7NEJBQ0NDLFNBQVN4RDs0QkFDVHFELFdBQVU7NEJBQ1ZJLE9BQU07NEJBQ05zQixjQUFXO3NDQUVYLDRFQUFDMUYsdUlBQUdBO2dDQUFDZ0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS25CLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJCO29DQUFHM0IsV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FFekQsOERBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7OzhEQUNDLDhEQUFDeUI7b0RBQU14QixXQUFVOztzRUFDZiw4REFBQzdELHlJQUFLQTs0REFBQzZELFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs4REFHL0IsOERBQUNDO29EQUFLRCxXQUFXLDhFQUErRyxPQUFqQzFCLGtCQUFrQjVCLElBQUk2QixTQUFTOzhEQUMzSDdCLElBQUk2QixTQUFTOzs7Ozs7Ozs7Ozs7c0RBS2xCLDhEQUFDd0I7OzhEQUNDLDhEQUFDeUI7b0RBQU14QixXQUFVOztzRUFDZiw4REFBQzNELDhJQUFVQTs0REFBQzJELFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs4REFHcEMsOERBQUNDO29EQUFLRCxXQUFVOzhEQUE2QnRELElBQUlrRixNQUFNOzs7Ozs7Ozs7Ozs7c0RBSXpELDhEQUFDN0I7OzhEQUNDLDhEQUFDeUI7b0RBQU14QixXQUFVOztzRUFDZiw4REFBQy9ELDBJQUFNQTs0REFBQytELFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs4REFHaEMsOERBQUNDO29EQUFLRCxXQUFVOzhEQUNicEQsWUFBWTs7Ozs7OzhEQUVmLDhEQUFDb0U7b0RBQUVoQixXQUFVOzhEQUE4QnRELElBQUltRixRQUFROzs7Ozs7Ozs7Ozs7c0RBSXpELDhEQUFDOUI7OzhEQUNDLDhEQUFDeUI7b0RBQU14QixXQUFVOztzRUFDZiw4REFBQzlELDhJQUFVQTs0REFBQzhELFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs4REFHcEMsOERBQUNDO29EQUFLRCxXQUFVOzhEQUFpQnJDLFdBQVdqQixJQUFJb0YsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUs1RHBGLElBQUlxRixXQUFXLGtCQUNkLDhEQUFDaEM7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDd0I7NENBQU14QixXQUFVOzs4REFDZiw4REFBQzVELDhJQUFVQTtvREFBQzRELFdBQVU7Ozs7OztnREFBWTs7Ozs7OztzREFHcEMsOERBQUNnQjs0Q0FBRWhCLFdBQVU7c0RBQ1Z0RCxJQUFJcUYsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU94Qiw4REFBQ2hDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJCO29DQUFHM0IsV0FBVTs4Q0FDWHRELElBQUlrQyxJQUFJLElBQUlJLGdCQUFnQnRDLElBQUlrQyxJQUFJLElBQUksWUFBWTs7Ozs7O2dDQUd0RGxDLElBQUlrQyxJQUFJLGlCQUNQLDhEQUFDbUI7OENBQ0VmLGdCQUFnQnRDLElBQUlrQyxJQUFJLGtCQUN2Qiw4REFBQ21COzswREFFQyw4REFBQ0E7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDd0I7d0RBQU14QixXQUFVOzs0REFBK0M7NERBQzlDdEQsSUFBSWtDLElBQUksQ0FBQ29ELFdBQVcsSUFBSTs0REFBRTs7Ozs7OztvREFFM0NwQixtQkFBbUJsRSxJQUFJa0MsSUFBSSxDQUFDSyxPQUFPOzs7Ozs7OzRDQUlyQzZCLE9BQU9tQixJQUFJLENBQUN2RixJQUFJa0MsSUFBSSxFQUFFc0QsSUFBSSxDQUFDL0UsQ0FBQUEsTUFBTyxDQUFDO29EQUFDO29EQUFXO29EQUFpQjtpREFBYyxDQUFDdUIsUUFBUSxDQUFDdkIsdUJBQ3ZGLDhEQUFDNEM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDd0I7d0RBQU14QixXQUFVO2tFQUErQzs7Ozs7O2tFQUdoRSw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNtQzs0REFBSW5DLFdBQVU7c0VBQ1pyQixlQUNDbUMsT0FBT3NCLFdBQVcsQ0FDaEJ0QixPQUFPQyxPQUFPLENBQUNyRSxJQUFJa0MsSUFBSSxFQUFFeUQsTUFBTSxDQUFDO29FQUFDLENBQUNsRixJQUFJO3VFQUNwQyxDQUFDO29FQUFDO29FQUFXO29FQUFpQjtpRUFBYyxDQUFDdUIsUUFBUSxDQUFDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQVV0RSw4REFBQzRDOzswREFDQyw4REFBQ3lCO2dEQUFNeEIsV0FBVTswREFBK0M7Ozs7OzswREFHaEUsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDbUM7b0RBQUluQyxXQUFVOzhEQUNackIsZUFBZWpDLElBQUlrQyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7eURBT2xDLDhEQUFDbUI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDM0QsOElBQVVBOzRDQUFDMkQsV0FBVTs7Ozs7O3NEQUN0Qiw4REFBQ2dCOzRDQUFFaEIsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNM0MsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3dCO29DQUFNeEIsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNzQztvQ0FBS3RDLFdBQVU7OENBQ2J0RCxJQUFJNkYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU1iLDhEQUFDeEM7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUNDQyxTQUFTeEQ7d0JBQ1RxRCxXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT1g7R0F6WWdCeEQ7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2xvZ3MvY29tcG9uZW50cy9Mb2dEZXRhaWxzTW9kYWwudHN4P2IyZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBGaVgsIEZpVXNlciwgRmlDYWxlbmRhciwgRmlUYWcsIEZpRmlsZVRleHQsIEZpRGF0YWJhc2UsIEZpRXllLCBGaUV5ZU9mZiB9IGZyb20gJ3JlYWN0LWljb25zL2ZpJztcbmltcG9ydCB0eXBlIHsgTG9nIH0gZnJvbSAnLi4vbG9nc0FwaSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgTG9nRGV0YWlsc01vZGFsUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIGxvZzogTG9nIHwgbnVsbDtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgdXNlck5hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2dEZXRhaWxzTW9kYWwoeyBpc09wZW4sIGxvZywgb25DbG9zZSwgdXNlck5hbWUgfTogTG9nRGV0YWlsc01vZGFsUHJvcHMpIHtcbiAgLy8gU3RhdGUgZm9yIHRvZ2dsaW5nIG9sZC9uZXcgZGF0YSB2aXNpYmlsaXR5XG4gIGNvbnN0IFtzaG93T2xkRGF0YSwgc2V0U2hvd09sZERhdGFdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtzaG93TmV3RGF0YSwgc2V0U2hvd05ld0RhdGFdID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgLy8gSGFuZGxlIGVzY2FwZSBrZXkgdG8gY2xvc2UgbW9kYWxcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzT3BlbikgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgICAgaWYgKGUua2V5ID09PSAnRXNjYXBlJykge1xuICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIG9uQ2xvc2UoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIFxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gIH0sIFtpc09wZW4sIG9uQ2xvc2VdKTtcblxuICAvLyBIYW5kbGUgY2xpY2tpbmcgb3V0c2lkZSBtb2RhbCB0byBjbG9zZVxuICBjb25zdCBoYW5kbGVPdmVybGF5Q2xpY2sgPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGlmIChlLnRhcmdldCA9PT0gZS5jdXJyZW50VGFyZ2V0KSB7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZvcm1hdCBkYXRlIGZvciBkaXNwbGF5XG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZTogRGF0ZSB8IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGRhdGVPYmogPSB0eXBlb2YgZGF0ZSA9PT0gJ3N0cmluZycgPyBuZXcgRGF0ZShkYXRlKSA6IGRhdGU7XG4gICAgcmV0dXJuIGRhdGVPYmoudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywge1xuICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgbW9udGg6ICdsb25nJyxcbiAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgbWludXRlOiAnMi1kaWdpdCcsXG4gICAgICBzZWNvbmQ6ICcyLWRpZ2l0JyxcbiAgICB9KTtcbiAgfTtcblxuICAvLyBHZXQgb3BlcmF0aW9uIGNvbG9yIGJhc2VkIG9uIG9wZXJhdGlvbiB0eXBlXG4gIGNvbnN0IGdldE9wZXJhdGlvbkNvbG9yID0gKG9wZXJhdGlvbjogc3RyaW5nKSA9PiB7XG4gICAgY29uc3Qgb3AgPSBvcGVyYXRpb24udG9Mb3dlckNhc2UoKTtcbiAgICBpZiAob3AuaW5jbHVkZXMoJ2NyZWF0ZScpIHx8IG9wLmluY2x1ZGVzKCdhZGQnKSkge1xuICAgICAgcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgYm9yZGVyLWdyZWVuLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygndXBkYXRlJykgfHwgb3AuaW5jbHVkZXMoJ2VkaXQnKSB8fCBvcC5pbmNsdWRlcygnbW9kaWZ5JykpIHtcbiAgICAgIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBib3JkZXItYmx1ZS0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ2RlbGV0ZScpIHx8IG9wLmluY2x1ZGVzKCdyZW1vdmUnKSkge1xuICAgICAgcmV0dXJuICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCBib3JkZXItcmVkLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygnY2FuY2VsJykgfHwgb3AuaW5jbHVkZXMoJ3JlamVjdCcpKSB7XG4gICAgICByZXR1cm4gJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwIGJvcmRlci1vcmFuZ2UtMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCdhcHByb3ZlJykgfHwgb3AuaW5jbHVkZXMoJ2NvbXBsZXRlJykpIHtcbiAgICAgIHJldHVybiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAgYm9yZGVyLXB1cnBsZS0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ3RyYW5zZmVyJykgfHwgb3AuaW5jbHVkZXMoJ21vdmUnKSkge1xuICAgICAgcmV0dXJuICdiZy1pbmRpZ28tMTAwIHRleHQtaW5kaWdvLTgwMCBib3JkZXItaW5kaWdvLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygnYWRqdXN0JykgfHwgb3AuaW5jbHVkZXMoJ21vZGlmeScpKSB7XG4gICAgICByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIGJvcmRlci15ZWxsb3ctMjAwJztcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwIGJvcmRlci1ncmF5LTIwMCc7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZvcm1hdCBKU09OIGRhdGEgZm9yIGRpc3BsYXlcbiAgY29uc3QgZm9ybWF0SnNvbkRhdGEgPSAoZGF0YTogUmVjb3JkPHN0cmluZywgYW55PikgPT4ge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoZGF0YSwgbnVsbCwgMik7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiAnSW52YWxpZCBKU09OIGRhdGEnO1xuICAgIH1cbiAgfTtcblxuICAvLyBDaGVjayBpZiB0aGUgbG9nIGRhdGEgY29udGFpbnMgZGVsdGEgY2hhbmdlc1xuICBjb25zdCBoYXNEZWx0YUNoYW5nZXMgPSAoZGF0YTogUmVjb3JkPHN0cmluZywgYW55PikgPT4ge1xuICAgIHJldHVybiBkYXRhICYmIGRhdGEuY2hhbmdlcyAmJiB0eXBlb2YgZGF0YS5jaGFuZ2VzID09PSAnb2JqZWN0JztcbiAgfTtcblxuXG5cbiAgLy8gUmVuZGVyIG92ZXJsYXBwZWQgYmVmb3JlL2FmdGVyIHZhbHVlcyB3aXRoIHRvZ2dsZSBjb250cm9sc1xuICBjb25zdCByZW5kZXJPdmVybGFwcGVkVmFsdWVzID0gKGJlZm9yZTogYW55LCBhZnRlcjogYW55KSA9PiB7XG4gICAgY29uc3QgYmVmb3JlU3RyID0gYmVmb3JlID09PSBudWxsID8gJ251bGwnIDpcbiAgICAgICAgICAgICAgICAgICAgIHR5cGVvZiBiZWZvcmUgPT09ICdvYmplY3QnID9cbiAgICAgICAgICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KGJlZm9yZSwgbnVsbCwgMikgOlxuICAgICAgICAgICAgICAgICAgICAgU3RyaW5nKGJlZm9yZSk7XG5cbiAgICBjb25zdCBhZnRlclN0ciA9IGFmdGVyID09PSBudWxsID8gJ251bGwnIDpcbiAgICAgICAgICAgICAgICAgICAgdHlwZW9mIGFmdGVyID09PSAnb2JqZWN0JyA/XG4gICAgICAgICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KGFmdGVyLCBudWxsLCAyKSA6XG4gICAgICAgICAgICAgICAgICAgIFN0cmluZyhhZnRlcik7XG5cbiAgICBjb25zdCBiZWZvcmVMaW5lcyA9IGJlZm9yZVN0ci5zcGxpdCgnXFxuJyk7XG4gICAgY29uc3QgYWZ0ZXJMaW5lcyA9IGFmdGVyU3RyLnNwbGl0KCdcXG4nKTtcbiAgICBjb25zdCBtYXhMaW5lcyA9IE1hdGgubWF4KGJlZm9yZUxpbmVzLmxlbmd0aCwgYWZ0ZXJMaW5lcy5sZW5ndGgpO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgd2l0aCBsZWdlbmQgYW5kIHRvZ2dsZSBjb250cm9scyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBweC0zIHB5LTIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIGJnLXJlZC0yMDAgYm9yZGVyIGJvcmRlci1yZWQtNDAwIHJvdW5kZWRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICBCZWZvcmVcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBiZy1ncmVlbi0yMDAgYm9yZGVyIGJvcmRlci1ncmVlbi00MDAgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIEFmdGVyXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVG9nZ2xlIENvbnRyb2xzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93T2xkRGF0YSghc2hvd09sZERhdGEpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTIgcHktMSB0ZXh0LXhzIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIHNob3dPbGREYXRhXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNzAwIGhvdmVyOmJnLXJlZC0yMDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMjAwIHRleHQtZ3JheS01MDAgaG92ZXI6YmctZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgdGl0bGU9e3Nob3dPbGREYXRhID8gJ0hpZGUgb2xkIGRhdGEnIDogJ1Nob3cgb2xkIGRhdGEnfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3Nob3dPbGREYXRhID8gPEZpRXllIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPiA6IDxGaUV5ZU9mZiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz59XG4gICAgICAgICAgICAgICAgT2xkXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TmV3RGF0YSghc2hvd05ld0RhdGEpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHB4LTIgcHktMSB0ZXh0LXhzIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgIHNob3dOZXdEYXRhXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTcwMCBob3ZlcjpiZy1ncmVlbi0yMDAnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLWdyYXktMjAwIHRleHQtZ3JheS01MDAgaG92ZXI6YmctZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgdGl0bGU9e3Nob3dOZXdEYXRhID8gJ0hpZGUgbmV3IGRhdGEnIDogJ1Nob3cgbmV3IGRhdGEnfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3Nob3dOZXdEYXRhID8gPEZpRXllIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPiA6IDxGaUV5ZU9mZiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz59XG4gICAgICAgICAgICAgICAgTmV3XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBtYXgtaC02NCBvdmVyZmxvdy1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiBtYXhMaW5lcyB9LCAoXywgaSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBiZWZvcmVMaW5lID0gYmVmb3JlTGluZXNbaV0gfHwgJyc7XG4gICAgICAgICAgICAgIGNvbnN0IGFmdGVyTGluZSA9IGFmdGVyTGluZXNbaV0gfHwgJyc7XG4gICAgICAgICAgICAgIGNvbnN0IGlzQ2hhbmdlZCA9IGJlZm9yZUxpbmUgIT09IGFmdGVyTGluZTtcblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtaW4taC1bMS41cmVtXVwiPlxuICAgICAgICAgICAgICAgICAge2lzQ2hhbmdlZCA/IChcbiAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQmVmb3JlIGxpbmUgd2l0aCBzdHJpa2V0aHJvdWdoICovfVxuICAgICAgICAgICAgICAgICAgICAgIHtiZWZvcmVMaW5lICYmIHNob3dPbGREYXRhICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGJnLXJlZC01MCBweC0xIGxpbmUtdGhyb3VnaCBvcGFjaXR5LTc1XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtiZWZvcmVMaW5lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQWZ0ZXIgbGluZSAqL31cbiAgICAgICAgICAgICAgICAgICAgICB7YWZ0ZXJMaW5lICYmIHNob3dOZXdEYXRhICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi03MDAgYmctZ3JlZW4tNTAgcHgtMSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWZ0ZXJMaW5lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAvKiBVbmNoYW5nZWQgbGluZSAtIHNob3cgaWYgZWl0aGVyIHRvZ2dsZSBpcyBvbiAqL1xuICAgICAgICAgICAgICAgICAgICAoc2hvd09sZERhdGEgfHwgc2hvd05ld0RhdGEpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgcHgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2JlZm9yZUxpbmV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG5cblxuICAvLyBSZW5kZXIgZGVsdGEgY2hhbmdlcyBpbiBhIHVzZXItZnJpZW5kbHkgZm9ybWF0XG4gIGNvbnN0IHJlbmRlckRlbHRhQ2hhbmdlcyA9IChjaGFuZ2VzOiBSZWNvcmQ8c3RyaW5nLCB7IGJlZm9yZTogYW55OyBhZnRlcjogYW55IH0+KSA9PiB7XG4gICAgY29uc3QgY2hhbmdlRW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGNoYW5nZXMpO1xuXG4gICAgaWYgKGNoYW5nZUVudHJpZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5ObyBjaGFuZ2VzIGRldGVjdGVkPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHtjaGFuZ2VFbnRyaWVzLm1hcCgoW2ZpZWxkLCBjaGFuZ2VdKSA9PiB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZH0gY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00IGNhcGl0YWxpemVcIj5cbiAgICAgICAgICAgICAgICB7ZmllbGQucmVwbGFjZSgvKFtBLVpdKS9nLCAnICQxJykucmVwbGFjZSgvXi4vLCBzdHIgPT4gc3RyLnRvVXBwZXJDYXNlKCkpfVxuICAgICAgICAgICAgICA8L2g1PlxuXG4gICAgICAgICAgICAgIHsvKiBCZWZvcmUvQWZ0ZXIgQ29tcGFyaXNvbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICDwn5OLIEJlZm9yZS9BZnRlciBDb21wYXJpc29uXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgU2hvd3MgY2hhbmdlcyBpbmxpbmUgd2l0aCBzdHJpa2V0aHJvdWdoIGZvciByZW1vdmVkIGNvbnRlbnQgYW5kIGhpZ2hsaWdodGluZyBmb3IgYWRkZWQgY29udGVudFxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICB7cmVuZGVyT3ZlcmxhcHBlZFZhbHVlcyhjaGFuZ2UuYmVmb3JlLCBjaGFuZ2UuYWZ0ZXIpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0pfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICBpZiAoIWlzT3BlbiB8fCAhbG9nKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIiBvbkNsaWNrPXtoYW5kbGVPdmVybGF5Q2xpY2t9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBtYXgtdy02eGwgdy1mdWxsIG14LTQgbWF4LWgtWzkwdmhdIG92ZXJmbG93LWhpZGRlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBmbGV4LXNocmluay0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICBMb2cgRGV0YWlsc1xuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgIFZpZXcgZGV0YWlsZWQgaW5mb3JtYXRpb24gYWJvdXQgdGhpcyBsb2cgZW50cnlcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmF5LTEwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgdGl0bGU9XCJDbG9zZVwiXG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xvc2UgbW9kYWxcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxGaVggY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHAtNlwiPlxuICAgICAgICAgIHsvKiBCYXNpYyBJbmZvcm1hdGlvbiAtIENvbXBhY3QgTGF5b3V0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5CYXNpYyBJbmZvcm1hdGlvbjwvaDQ+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7LyogT3BlcmF0aW9uICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEZpVGFnIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgT3BlcmF0aW9uXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIGJvcmRlciAke2dldE9wZXJhdGlvbkNvbG9yKGxvZy5vcGVyYXRpb24pfWB9PlxuICAgICAgICAgICAgICAgICAge2xvZy5vcGVyYXRpb259XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogRW50aXR5ICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEZpRGF0YWJhc2UgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBFbnRpdHlcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZm9udC1tZWRpdW1cIj57bG9nLmVudGl0eX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBVc2VyICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEZpVXNlciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIFVzZXJcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyTmFtZSB8fCAnVW5rbm93biBVc2VyJ31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj57bG9nLnVzZXJVdWlkfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFRpbWVzdGFtcCAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaUNhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgVGltZXN0YW1wXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e2Zvcm1hdERhdGUobG9nLmNyZWF0ZWRBdCl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRGVzY3JpcHRpb24gLSBGdWxsIFdpZHRoICovfVxuICAgICAgICAgICAge2xvZy5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEZpRmlsZVRleHQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBEZXNjcmlwdGlvblxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCBiZy1ncmF5LTUwIHAtMyByb3VuZGVkLWxnIGJvcmRlclwiPlxuICAgICAgICAgICAgICAgICAge2xvZy5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBEYXRhIFNlY3Rpb24gLSBGdWxsIFdpZHRoICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAge2xvZy5kYXRhICYmIGhhc0RlbHRhQ2hhbmdlcyhsb2cuZGF0YSkgPyAnQ2hhbmdlcycgOiAnQWRkaXRpb25hbCBEYXRhJ31cbiAgICAgICAgICAgIDwvaDQ+XG5cbiAgICAgICAgICAgIHtsb2cuZGF0YSA/IChcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICB7aGFzRGVsdGFDaGFuZ2VzKGxvZy5kYXRhKSA/IChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBEZWx0YSBDaGFuZ2VzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEZpZWxkIENoYW5nZXMgKHtsb2cuZGF0YS5jaGFuZ2VDb3VudCB8fCAwfSBjaGFuZ2VzKVxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAge3JlbmRlckRlbHRhQ2hhbmdlcyhsb2cuZGF0YS5jaGFuZ2VzKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEFkZGl0aW9uYWwgbWV0YWRhdGEgKi99XG4gICAgICAgICAgICAgICAgICAgIHtPYmplY3Qua2V5cyhsb2cuZGF0YSkuc29tZShrZXkgPT4gIVsnY2hhbmdlcycsICdjaGFuZ2VkRmllbGRzJywgJ2NoYW5nZUNvdW50J10uaW5jbHVkZXMoa2V5KSkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEFkZGl0aW9uYWwgSW5mb3JtYXRpb25cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtZ3JlZW4tNDAwIHAtNCByb3VuZGVkLWxnIGJvcmRlciBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbW9ubyB3aGl0ZXNwYWNlLXByZS13cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEpzb25EYXRhKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmZyb21FbnRyaWVzKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBPYmplY3QuZW50cmllcyhsb2cuZGF0YSkuZmlsdGVyKChba2V5XSkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAhWydjaGFuZ2VzJywgJ2NoYW5nZWRGaWVsZHMnLCAnY2hhbmdlQ291bnQnXS5pbmNsdWRlcyhrZXkpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIEpTT04gRGF0YVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtZ3JlZW4tNDAwIHAtNCByb3VuZGVkLWxnIGJvcmRlciBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vIHdoaXRlc3BhY2UtcHJlLXdyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRKc29uRGF0YShsb2cuZGF0YSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICA8RmlEYXRhYmFzZSBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTMwMCBteC1hdXRvIG1iLTNcIiAvPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPk5vIGFkZGl0aW9uYWwgZGF0YSBhdmFpbGFibGU8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBMb2cgSUQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIExvZyBJRFxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBiZy1ncmF5LTEwMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICB7bG9nLmlkfVxuICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRm9vdGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZmxleC1zaHJpbmstMCBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZ3JheS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiRmlYIiwiRmlVc2VyIiwiRmlDYWxlbmRhciIsIkZpVGFnIiwiRmlGaWxlVGV4dCIsIkZpRGF0YWJhc2UiLCJGaUV5ZSIsIkZpRXllT2ZmIiwiTG9nRGV0YWlsc01vZGFsIiwiaXNPcGVuIiwibG9nIiwib25DbG9zZSIsInVzZXJOYW1lIiwic2hvd09sZERhdGEiLCJzZXRTaG93T2xkRGF0YSIsInNob3dOZXdEYXRhIiwic2V0U2hvd05ld0RhdGEiLCJoYW5kbGVLZXlEb3duIiwiZSIsImtleSIsInByZXZlbnREZWZhdWx0Iiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVPdmVybGF5Q2xpY2siLCJ0YXJnZXQiLCJjdXJyZW50VGFyZ2V0IiwiZm9ybWF0RGF0ZSIsImRhdGUiLCJkYXRlT2JqIiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsInNlY29uZCIsImdldE9wZXJhdGlvbkNvbG9yIiwib3BlcmF0aW9uIiwib3AiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiZm9ybWF0SnNvbkRhdGEiLCJkYXRhIiwiSlNPTiIsInN0cmluZ2lmeSIsImVycm9yIiwiaGFzRGVsdGFDaGFuZ2VzIiwiY2hhbmdlcyIsInJlbmRlck92ZXJsYXBwZWRWYWx1ZXMiLCJiZWZvcmUiLCJhZnRlciIsImJlZm9yZVN0ciIsIlN0cmluZyIsImFmdGVyU3RyIiwiYmVmb3JlTGluZXMiLCJzcGxpdCIsImFmdGVyTGluZXMiLCJtYXhMaW5lcyIsIk1hdGgiLCJtYXgiLCJsZW5ndGgiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwiQXJyYXkiLCJmcm9tIiwiXyIsImkiLCJiZWZvcmVMaW5lIiwiYWZ0ZXJMaW5lIiwiaXNDaGFuZ2VkIiwicmVuZGVyRGVsdGFDaGFuZ2VzIiwiY2hhbmdlRW50cmllcyIsIk9iamVjdCIsImVudHJpZXMiLCJwIiwibWFwIiwiZmllbGQiLCJjaGFuZ2UiLCJoNSIsInJlcGxhY2UiLCJzdHIiLCJ0b1VwcGVyQ2FzZSIsImxhYmVsIiwiaDMiLCJhcmlhLWxhYmVsIiwiaDQiLCJlbnRpdHkiLCJ1c2VyVXVpZCIsImNyZWF0ZWRBdCIsImRlc2NyaXB0aW9uIiwiY2hhbmdlQ291bnQiLCJrZXlzIiwic29tZSIsInByZSIsImZyb21FbnRyaWVzIiwiZmlsdGVyIiwiY29kZSIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\n"));

/***/ })

});