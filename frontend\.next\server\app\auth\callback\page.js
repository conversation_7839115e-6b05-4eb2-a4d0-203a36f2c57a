/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/callback/page";
exports.ids = ["app/auth/callback/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'callback',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/callback/page.tsx */ \"(rsc)/./app/auth/callback/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/callback/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/callback/page\",\n        pathname: \"/auth/callback\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/callback/page.tsx */ \"(ssr)/./app/auth/callback/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2NhbGxiYWNrJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2RpZG8tZGlzdHJpYnV0aW9uLWZyb250ZW5kLz80ZmYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWFobGxcXFxcRG9jdW1lbnRzXFxcXHdvcmtzcGFjZVxcXFxwcm9qZWN0c1xcXFxkaWRvLWRpc3RyaWJ1dGlvblxcXFxmcm9udGVuZFxcXFxhcHBcXFxcYXV0aFxcXFxjYWxsYmFja1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Ccallback%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNtYWhsbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q3dvcmtzcGFjZSU1QyU1Q3Byb2plY3RzJTVDJTVDZGlkby1kaXN0cmlidXRpb24lNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWFobGwlNUMlNUNEb2N1bWVudHMlNUMlNUN3b3Jrc3BhY2UlNUMlNUNwcm9qZWN0cyU1QyU1Q2RpZG8tZGlzdHJpYnV0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQStLO0FBQy9LO0FBQ0Esb09BQWdMO0FBQ2hMO0FBQ0EsME9BQW1MO0FBQ25MO0FBQ0Esd09BQWtMO0FBQ2xMO0FBQ0Esa1BBQXVMO0FBQ3ZMO0FBQ0Esc1FBQWlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvP2I4ZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhsbFxcXFxEb2N1bWVudHNcXFxcd29ya3NwYWNlXFxcXHByb2plY3RzXFxcXGRpZG8tZGlzdHJpYnV0aW9uXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWFobGxcXFxcRG9jdW1lbnRzXFxcXHdvcmtzcGFjZVxcXFxwcm9qZWN0c1xcXFxkaWRvLWRpc3RyaWJ1dGlvblxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhsbFxcXFxEb2N1bWVudHNcXFxcd29ya3NwYWNlXFxcXHByb2plY3RzXFxcXGRpZG8tZGlzdHJpYnV0aW9uXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1haGxsXFxcXERvY3VtZW50c1xcXFx3b3Jrc3BhY2VcXFxccHJvamVjdHNcXFxcZGlkby1kaXN0cmlidXRpb25cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhsbFxcXFxEb2N1bWVudHNcXFxcd29ya3NwYWNlXFxcXHByb2plY3RzXFxcXGRpZG8tZGlzdHJpYnV0aW9uXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhsbFxcXFxEb2N1bWVudHNcXFxcd29ya3NwYWNlXFxcXHByb2plY3RzXFxcXGRpZG8tZGlzdHJpYnV0aW9uXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/auth/callback/page.tsx":
/*!************************************!*\
  !*** ./app/auth/callback/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/NetworkStatusChecker */ \"(ssr)/./components/NetworkStatusChecker.tsx\");\n/* harmony import */ var _components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BackendStatusChecker */ \"(ssr)/./components/BackendStatusChecker.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AuthCallbackPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeoutId, setTimeoutId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [debugInfo, setDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [networkStatus, setNetworkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [authStep, setAuthStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"initializing\");\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showDebugInfo, setShowDebugInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const MAX_RETRIES = 3;\n    const AUTH_TIMEOUT = 30000; // 30 seconds\n    const BACKEND_TIMEOUT = 10000; // 10 seconds\n    const addDebugInfo = (info)=>{\n        console.log(`[Google Auth Debug] ${info}`);\n        setDebugInfo((prev)=>[\n                ...prev,\n                `${new Date().toLocaleTimeString()}: ${info}`\n            ]);\n    };\n    const clearAllTimeouts = ()=>{\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n            setTimeoutId(null);\n        }\n    };\n    const handleAuth = async (isRetry = false)=>{\n        if (isRetry) {\n            setIsRetrying(true);\n            setError(null);\n            setDebugInfo([]);\n            setAuthStep(\"initializing\");\n            setRetryCount((prev)=>prev + 1);\n        }\n        addDebugInfo(`Starting Google authentication process... ${isRetry ? `(Retry #${retryCount + 1})` : \"\"}`);\n        setAuthStep(\"checking_backend\");\n        // Clear any existing timeouts\n        clearAllTimeouts();\n        try {\n            // Check backend connectivity first\n            addDebugInfo(\"Checking backend connectivity...\");\n            try {\n                const healthCheck = await fetch(\"/api/auth/health\", {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    signal: AbortSignal.timeout(BACKEND_TIMEOUT)\n                });\n                if (!healthCheck.ok) {\n                    throw new Error(`Backend health check failed: ${healthCheck.status}`);\n                }\n                addDebugInfo(\"Backend is accessible, proceeding with authentication\");\n                setAuthStep(\"processing_auth\");\n            } catch (healthError) {\n                addDebugInfo(`Backend health check failed: ${healthError.message}`);\n                throw new Error(\"Backend server is not accessible. Please ensure:\\n\\n1. Backend server is running on localhost:8000\\n2. No firewall is blocking the connection\\n3. Check backend logs for any startup errors\");\n            }\n            // Set main authentication timeout\n            const timeout = setTimeout(()=>{\n                addDebugInfo(\"Authentication timeout reached (30 seconds)\");\n                setAuthStep(\"failed\");\n                setError(\"Authentication is taking longer than expected. This could be due to:\\n\\n1. Slow or unstable internet connection\\n2. Backend server not responding\\n3. Google OAuth configuration issues\\n\\nPlease try refreshing the page or check your connection.\");\n                // Auto-retry if we haven't exceeded max retries\n                if (retryCount < MAX_RETRIES) {\n                    addDebugInfo(`Auto-retrying in 5 seconds... (${retryCount + 1}/${MAX_RETRIES})`);\n                    setTimeout(()=>{\n                        handleAuth(true);\n                    }, 5000);\n                } else {\n                    addDebugInfo(\"Max retries reached, redirecting to auth page\");\n                    setTimeout(()=>{\n                        router.replace(\"/auth?error=callback_failed\");\n                    }, 3000);\n                }\n            }, AUTH_TIMEOUT);\n            setTimeoutId(timeout);\n            // Try to extract token and user from query params first\n            const params = new URLSearchParams(window.location.search);\n            addDebugInfo(`URL parameters found: ${Array.from(params.keys()).join(\", \")}`);\n            if (params.has(\"token\") && params.has(\"user\")) {\n                addDebugInfo(\"Found token and user in URL parameters\");\n                const token = params.get(\"token\");\n                const userData = JSON.parse(decodeURIComponent(params.get(\"user\")));\n                // Clear timeout since we got the data\n                clearAllTimeouts();\n                addDebugInfo(\"Successfully parsed user data from URL\");\n                console.log(\"Received user data:\", userData);\n                // Handle both legacy and enhanced response formats\n                if (userData.accessToken && userData.refreshToken) {\n                    addDebugInfo(\"Using enhanced response format\");\n                    login(userData.user, userData.accessToken, userData.refreshToken);\n                } else {\n                    addDebugInfo(\"Using legacy response format\");\n                    // Legacy response format - convert to new format\n                    const legacyUser = {\n                        uuid: userData.uuid,\n                        email: userData.email,\n                        firstName: userData.name ? userData.name.split(\" \")[0] : null,\n                        lastName: userData.name ? userData.name.split(\" \").slice(1).join(\" \") : null,\n                        phone: null,\n                        isActive: !userData.isDeleted,\n                        roles: [],\n                        warehouseUuid: userData.warehouseUuidString || userData.warehouseUuid || null,\n                        warehouseUuidString: userData.warehouseUuidString || userData.warehouseUuid || null,\n                        vanUuid: userData.vanUuidString || userData.vanUuid || null,\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString(),\n                        // Legacy support fields\n                        name: userData.name,\n                        userType: userData.userType,\n                        warehouseName: null\n                    };\n                    console.log(\"Converted user data:\", legacyUser);\n                    login(legacyUser, token, \"\"); // No refresh token in legacy format\n                }\n                addDebugInfo(\"Login successful, redirecting to dashboard\");\n                setAuthStep(\"completed\");\n                router.replace(\"/dashboard\");\n                return;\n            }\n            // If no query params, try to fetch from backend callback endpoint\n            addDebugInfo(\"No URL parameters found, trying backend callback endpoint\");\n            const controller = new AbortController();\n            const fetchTimeout = setTimeout(()=>{\n                addDebugInfo(\"Backend fetch timeout reached\");\n                controller.abort();\n            }, BACKEND_TIMEOUT);\n            let res;\n            try {\n                res = await fetch(\"/api/auth/google/callback\", {\n                    credentials: \"include\",\n                    signal: controller.signal\n                });\n            } catch (fetchError) {\n                addDebugInfo(`Primary callback endpoint failed: ${fetchError.message}`);\n                addDebugInfo(\"Trying JSON fallback endpoint...\");\n                // Try the JSON fallback endpoint\n                try {\n                    res = await fetch(\"/api/auth/google/callback/json\", {\n                        credentials: \"include\",\n                        signal: controller.signal\n                    });\n                    addDebugInfo(\"JSON fallback endpoint response received\");\n                } catch (fallbackError) {\n                    addDebugInfo(`JSON fallback endpoint also failed: ${fallbackError.message}`);\n                    throw fetchError; // Throw the original error\n                }\n            }\n            clearTimeout(fetchTimeout);\n            clearAllTimeouts();\n            addDebugInfo(`Backend response status: ${res.status} ${res.statusText}`);\n            if (res.ok) {\n                const data = await res.json();\n                addDebugInfo(\"Successfully received data from backend\");\n                if (data.user && data.accessToken) {\n                    // Handle enhanced response format\n                    if (data.refreshToken) {\n                        addDebugInfo(\"Using enhanced response format from backend\");\n                        login(data.user, data.accessToken, data.refreshToken);\n                    } else {\n                        addDebugInfo(\"Using legacy response format from backend\");\n                        // Legacy format\n                        login(data.user, data.accessToken, \"\");\n                    }\n                    addDebugInfo(\"Login successful, redirecting to dashboard\");\n                    setAuthStep(\"completed\");\n                    router.replace(\"/dashboard\");\n                    return;\n                } else {\n                    addDebugInfo(\"Backend response missing user or accessToken\");\n                    throw new Error(\"Invalid response from backend: missing user or access token\");\n                }\n            } else {\n                addDebugInfo(`Backend returned error status: ${res.status}`);\n                const errorText = await res.text();\n                addDebugInfo(`Backend error response: ${errorText}`);\n                throw new Error(`Backend authentication failed: ${res.status} ${res.statusText}`);\n            }\n        } catch (e) {\n            console.error(\"Authentication error:\", e);\n            // Clear any existing timeout\n            clearAllTimeouts();\n            const errorMessage = e instanceof Error ? e.message : \"Authentication failed. Please try again.\";\n            addDebugInfo(`Authentication failed: ${errorMessage}`);\n            setError(errorMessage);\n            setAuthStep(\"failed\");\n            // Auto-retry if we haven't exceeded max retries\n            if (retryCount < MAX_RETRIES) {\n                addDebugInfo(`Auto-retrying in 5 seconds... (${retryCount + 1}/${MAX_RETRIES})`);\n                setTimeout(()=>{\n                    handleAuth(true);\n                }, 5000);\n            } else {\n                addDebugInfo(\"Max retries reached, redirecting to auth page\");\n                setTimeout(()=>{\n                    router.replace(\"/auth?error=callback_failed\");\n                }, 3000);\n            }\n        } finally{\n            setIsRetrying(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        addDebugInfo(\"Callback page mounted\");\n        handleAuth();\n        // Global safety timeout - if nothing happens within 20 seconds, redirect to auth page\n        const safetyTimeout = setTimeout(()=>{\n            addDebugInfo(\"Global safety timeout reached - redirecting to auth page\");\n            router.replace(\"/auth?error=timeout\");\n        }, 20000); // 20 seconds\n        // Cleanup timeout on unmount\n        return ()=>{\n            clearAllTimeouts();\n            clearTimeout(safetyTimeout);\n        };\n    }, []);\n    const handleRetry = ()=>{\n        clearAllTimeouts();\n        handleAuth(true);\n    };\n    const handleCancel = ()=>{\n        clearAllTimeouts();\n        addDebugInfo(\"User cancelled authentication\");\n        router.replace(\"/auth\");\n    };\n    const handleBackendCheck = async ()=>{\n        addDebugInfo(\"Checking backend connectivity...\");\n        try {\n            const res = await fetch(\"/api/auth/health\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            addDebugInfo(`Backend connectivity check: ${res.status} ${res.statusText}`);\n            if (res.ok) {\n                const data = await res.json();\n                setError(`Backend is accessible (uptime: ${Math.floor(data.uptime)}s). The issue might be with Google OAuth configuration.`);\n            } else {\n                setError(\"Backend is accessible but returned an error. Check Google OAuth setup.\");\n            }\n        } catch (err) {\n            addDebugInfo(`Backend connectivity check failed: ${err.message}`);\n            setError(\"Cannot connect to backend server. Please ensure the backend is running on localhost:8000\");\n        }\n    };\n    const getStepMessage = ()=>{\n        switch(authStep){\n            case \"initializing\":\n                return \"Initializing authentication process...\";\n            case \"checking_backend\":\n                return \"Checking backend server connectivity...\";\n            case \"processing_auth\":\n                return \"Processing Google authentication...\";\n            case \"completed\":\n                return \"Authentication completed successfully!\";\n            case \"failed\":\n                return \"Authentication failed. Please try again.\";\n            default:\n                return \"Processing authentication...\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full text-center\",\n            children: [\n                error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-red-500 mx-auto mb-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                    children: \"Authentication Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6 whitespace-pre-line text-left\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this),\n                                retryCount > 0 && retryCount < MAX_RETRIES && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800\",\n                                        children: [\n                                            \"Retry #\",\n                                            retryCount,\n                                            \" of \",\n                                            MAX_RETRIES,\n                                            \" - Auto-retrying in 5 seconds...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRetry,\n                                    disabled: isRetrying,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isRetrying ? \"Retrying...\" : \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackendCheck,\n                                    className: \"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\",\n                                    children: \"Check Backend\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCancel,\n                                    className: \"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                    children: \"Signing you in with Google...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: getStepMessage()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: \"This process may take up to 30 seconds for unstable connections.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400 mb-4\",\n                                    children: \"If your internet is slow, this may take longer. Please be patient.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                retryCount > 0 && retryCount < MAX_RETRIES && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800\",\n                                        children: [\n                                            \"Retry #\",\n                                            retryCount,\n                                            \" of \",\n                                            MAX_RETRIES,\n                                            \" - Auto-retrying in 5 seconds...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                onStatusChange: setBackendStatus,\n                                                showDetails: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                onStatusChange: setNetworkStatus,\n                                                showDetails: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCancel,\n                            className: \"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowDebugInfo(!showDebugInfo),\n                            className: \"flex items-center text-sm text-gray-500 hover:text-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: `w-4 h-4 mr-1 transition-transform ${showDebugInfo ? \"rotate-90\" : \"\"}`,\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                \"Debug Information (\",\n                                debugInfo.length,\n                                \" entries)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        showDebugInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 p-3 bg-gray-100 rounded text-xs font-mono max-h-40 overflow-y-auto\",\n                            children: debugInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-1\",\n                                    children: info\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\callback\\\\page.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/callback/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 2 times for other errors\n                return failureCount < 2;\n            },\n            // Global error handler for queries\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Query failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        },\n        mutations: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 1 time for other errors\n                return failureCount < 1;\n            },\n            // Global error handler for mutations\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Mutation failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        }\n    }\n});\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/BackendStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/BackendStatusChecker.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BackendStatusChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BackendStatusChecker({ onStatusChange, showDetails = false, className = \"\" }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkBackendStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking backend connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Backend check timed out after 10 seconds. Server may be offline or unreachable.\");\n            onStatusChange?.(\"offline\");\n        }, 10000);\n        setCheckTimeout(timeout);\n        try {\n            const startTime = Date.now();\n            const response = await fetch(\"/api/auth/health\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: AbortSignal.timeout(8000)\n            });\n            const endTime = Date.now();\n            const responseTime = endTime - startTime;\n            // Clear the timeout since we got a response\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (response.ok) {\n                const data = await response.json();\n                setStatus(\"online\");\n                setDetails(`Backend is online (${responseTime}ms) - Uptime: ${Math.floor(data.uptime)}s`);\n                onStatusChange?.(\"online\");\n            } else {\n                setStatus(\"error\");\n                setDetails(`Backend returned error: ${response.status} ${response.statusText} (${responseTime}ms)`);\n                onStatusChange?.(\"error\");\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(`Cannot connect to backend: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n            onStatusChange?.(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this);\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking backend connection...\";\n            case \"online\":\n                return \"Backend server is online\";\n            case \"offline\":\n                return \"Backend server is offline\";\n            case \"error\":\n                return \"Backend server error\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"online\":\n                return \"text-green-600\";\n            case \"offline\":\n                return \"text-red-600\";\n            case \"error\":\n                return \"text-yellow-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm font-medium ${getStatusColor()}`,\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkBackendStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh backend status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/BackendStatusChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NetworkStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/NetworkStatusChecker.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NetworkStatusChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NetworkStatusChecker({ onStatusChange, showDetails = false, className = \"\" }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectionType, setConnectionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkNetworkStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking network connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Network check timed out after 15 seconds. No internet connection detected.\");\n            onStatusChange?.(\"offline\");\n        }, 15000);\n        setCheckTimeout(timeout);\n        try {\n            // Check connection type\n            if (\"connection\" in navigator) {\n                const conn = navigator.connection;\n                if (conn) {\n                    setConnectionType(`${conn.effectiveType || \"unknown\"} (${conn.type || \"unknown\"})`);\n                }\n            }\n            // Test connection speed by making multiple requests with shorter timeouts\n            const testUrls = [\n                \"https://www.google.com/favicon.ico\",\n                \"https://www.cloudflare.com/favicon.ico\",\n                \"https://www.github.com/favicon.ico\"\n            ];\n            const results = await Promise.allSettled(testUrls.map((url)=>fetch(url, {\n                    method: \"HEAD\",\n                    mode: \"no-cors\",\n                    cache: \"no-cache\",\n                    signal: AbortSignal.timeout(5000) // 5 second timeout per request\n                })));\n            const successfulRequests = results.filter((result)=>result.status === \"fulfilled\").length;\n            const totalRequests = results.length;\n            // Clear the timeout since we got results\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (successfulRequests === 0) {\n                setStatus(\"offline\");\n                setDetails(\"No internet connection detected. Please check your network connection.\");\n                onStatusChange?.(\"offline\");\n            } else if (successfulRequests < totalRequests) {\n                setStatus(\"unstable\");\n                setDetails(`Unstable connection: ${successfulRequests}/${totalRequests} requests successful. Some requests failed.`);\n                onStatusChange?.(\"unstable\");\n            } else {\n                // All requests successful, now test speed\n                const startTime = Date.now();\n                try {\n                    await fetch(\"https://www.google.com/favicon.ico\", {\n                        method: \"HEAD\",\n                        mode: \"no-cors\",\n                        cache: \"no-cache\",\n                        signal: AbortSignal.timeout(3000) // 3 second timeout for speed test\n                    });\n                    const endTime = Date.now();\n                    const responseTime = endTime - startTime;\n                    if (responseTime < 1000) {\n                        setStatus(\"good\");\n                        setDetails(`Good connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"good\");\n                    } else if (responseTime < 3000) {\n                        setStatus(\"slow\");\n                        setDetails(`Slow connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"slow\");\n                    } else {\n                        setStatus(\"unstable\");\n                        setDetails(`Very slow connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"unstable\");\n                    }\n                } catch (error) {\n                    setStatus(\"unstable\");\n                    setDetails(\"Connection test failed\");\n                    onStatusChange?.(\"unstable\");\n                }\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(`Network check failed: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n            onStatusChange?.(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkNetworkStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this);\n            case \"good\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this);\n            case \"slow\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this);\n            case \"unstable\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-orange-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking network connection...\";\n            case \"good\":\n                return \"Network connection is good\";\n            case \"slow\":\n                return \"Network connection is slow\";\n            case \"unstable\":\n                return \"Network connection is unstable\";\n            case \"offline\":\n                return \"No network connection\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"good\":\n                return \"text-green-600\";\n            case \"slow\":\n                return \"text-yellow-600\";\n            case \"unstable\":\n                return \"text-orange-600\";\n            case \"offline\":\n                return \"text-red-600\";\n        }\n    };\n    const getConnectionAdvice = ()=>{\n        switch(status){\n            case \"good\":\n                return null;\n            case \"slow\":\n                return \"Your connection is slow. Authentication may take longer than usual.\";\n            case \"unstable\":\n                return \"Your connection is unstable. Consider using a wired connection or moving closer to your router.\";\n            case \"offline\":\n                return \"No internet connection detected. Please check your network settings.\";\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm font-medium ${getStatusColor()}`,\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && connectionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Connection: \",\n                            connectionType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && getConnectionAdvice() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-orange-600 mt-1 font-medium\",\n                        children: getConnectionAdvice()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkNetworkStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh network status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL05ldHdvcmtTdGF0dXNDaGVja2VyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDbUQ7QUFRcEMsU0FBU0cscUJBQXFCLEVBQzNDQyxjQUFjLEVBQ2RDLGNBQWMsS0FBSyxFQUNuQkMsWUFBWSxFQUFFLEVBQ1k7SUFDMUIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdQLCtDQUFRQSxDQUF3RDtJQUM1RixNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQVM7SUFDL0MsTUFBTSxDQUFDVSxXQUFXQyxhQUFhLEdBQUdYLCtDQUFRQSxDQUFjO0lBQ3hELE1BQU0sQ0FBQ1ksZ0JBQWdCQyxrQkFBa0IsR0FBR2IsK0NBQVFBLENBQVM7SUFDN0QsTUFBTSxDQUFDYyxjQUFjQyxnQkFBZ0IsR0FBR2YsK0NBQVFBLENBQXdCO0lBRXhFLE1BQU1nQixxQkFBcUI7UUFDekJULFVBQVU7UUFDVkUsV0FBVztRQUVYLDZCQUE2QjtRQUM3QixJQUFJSyxjQUFjO1lBQ2hCRyxhQUFhSDtRQUNmO1FBRUEsOEJBQThCO1FBQzlCLE1BQU1JLFVBQVVDLFdBQVc7WUFDekJaLFVBQVU7WUFDVkUsV0FBVztZQUNYTixpQkFBaUI7UUFDbkIsR0FBRztRQUVIWSxnQkFBZ0JHO1FBRWhCLElBQUk7WUFDRix3QkFBd0I7WUFDeEIsSUFBSSxnQkFBZ0JFLFdBQVc7Z0JBQzdCLE1BQU1DLE9BQU8sVUFBbUJDLFVBQVU7Z0JBQzFDLElBQUlELE1BQU07b0JBQ1JSLGtCQUFrQixDQUFDLEVBQUVRLEtBQUtFLGFBQWEsSUFBSSxVQUFVLEVBQUUsRUFBRUYsS0FBS0csSUFBSSxJQUFJLFVBQVUsQ0FBQyxDQUFDO2dCQUNwRjtZQUNGO1lBRUEsMEVBQTBFO1lBQzFFLE1BQU1DLFdBQVc7Z0JBQ2Y7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVELE1BQU1DLFVBQVUsTUFBTUMsUUFBUUMsVUFBVSxDQUN0Q0gsU0FBU0ksR0FBRyxDQUFDQyxDQUFBQSxNQUNYQyxNQUFNRCxLQUFLO29CQUNURSxRQUFRO29CQUNSQyxNQUFNO29CQUNOQyxPQUFPO29CQUNQQyxRQUFRQyxZQUFZbEIsT0FBTyxDQUFDLE1BQU0sK0JBQStCO2dCQUNuRTtZQUlKLE1BQU1tQixxQkFBcUJYLFFBQVFZLE1BQU0sQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBT2pDLE1BQU0sS0FBSyxhQUFha0MsTUFBTTtZQUN6RixNQUFNQyxnQkFBZ0JmLFFBQVFjLE1BQU07WUFFcEMseUNBQXlDO1lBQ3pDdkIsYUFBYUM7WUFDYkgsZ0JBQWdCO1lBRWhCSixhQUFhLElBQUkrQjtZQUVqQixJQUFJTCx1QkFBdUIsR0FBRztnQkFDNUI5QixVQUFVO2dCQUNWRSxXQUFXO2dCQUNYTixpQkFBaUI7WUFDbkIsT0FBTyxJQUFJa0MscUJBQXFCSSxlQUFlO2dCQUM3Q2xDLFVBQVU7Z0JBQ1ZFLFdBQVcsQ0FBQyxxQkFBcUIsRUFBRTRCLG1CQUFtQixDQUFDLEVBQUVJLGNBQWMsMkNBQTJDLENBQUM7Z0JBQ25IdEMsaUJBQWlCO1lBQ25CLE9BQU87Z0JBQ0wsMENBQTBDO2dCQUMxQyxNQUFNd0MsWUFBWUQsS0FBS0UsR0FBRztnQkFDMUIsSUFBSTtvQkFDRixNQUFNYixNQUFNLHNDQUFzQzt3QkFDaERDLFFBQVE7d0JBQ1JDLE1BQU07d0JBQ05DLE9BQU87d0JBQ1BDLFFBQVFDLFlBQVlsQixPQUFPLENBQUMsTUFBTSxrQ0FBa0M7b0JBQ3RFO29CQUNBLE1BQU0yQixVQUFVSCxLQUFLRSxHQUFHO29CQUN4QixNQUFNRSxlQUFlRCxVQUFVRjtvQkFFL0IsSUFBSUcsZUFBZSxNQUFNO3dCQUN2QnZDLFVBQVU7d0JBQ1ZFLFdBQVcsQ0FBQyxpQkFBaUIsRUFBRXFDLGFBQWEsZ0JBQWdCLENBQUM7d0JBQzdEM0MsaUJBQWlCO29CQUNuQixPQUFPLElBQUkyQyxlQUFlLE1BQU07d0JBQzlCdkMsVUFBVTt3QkFDVkUsV0FBVyxDQUFDLGlCQUFpQixFQUFFcUMsYUFBYSxnQkFBZ0IsQ0FBQzt3QkFDN0QzQyxpQkFBaUI7b0JBQ25CLE9BQU87d0JBQ0xJLFVBQVU7d0JBQ1ZFLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRXFDLGFBQWEsZ0JBQWdCLENBQUM7d0JBQ2xFM0MsaUJBQWlCO29CQUNuQjtnQkFDRixFQUFFLE9BQU80QyxPQUFPO29CQUNkeEMsVUFBVTtvQkFDVkUsV0FBVztvQkFDWE4saUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0YsRUFBRSxPQUFPNEMsT0FBTztZQUNkLDBDQUEwQztZQUMxQzlCLGFBQWFDO1lBQ2JILGdCQUFnQjtZQUVoQkosYUFBYSxJQUFJK0I7WUFDakJuQyxVQUFVO1lBQ1ZFLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRXNDLGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHLGdCQUFnQixDQUFDO1lBQzlGOUMsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQUYsZ0RBQVNBLENBQUM7UUFDUmU7UUFFQSw2QkFBNkI7UUFDN0IsT0FBTztZQUNMLElBQUlGLGNBQWM7Z0JBQ2hCRyxhQUFhSDtZQUNmO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNb0MsZ0JBQWdCO1FBQ3BCLE9BQVE1QztZQUNOLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUM2QztvQkFBSTlDLFdBQVU7Ozs7OztZQUVuQixLQUFLO2dCQUNILHFCQUNFLDhEQUFDK0M7b0JBQUkvQyxXQUFVO29CQUF5QmdELE1BQUs7b0JBQWVDLFNBQVE7OEJBQ2xFLDRFQUFDQzt3QkFBS0MsVUFBUzt3QkFBVUMsR0FBRTt3QkFBd0lDLFVBQVM7Ozs7Ozs7Ozs7O1lBR2xMLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNOO29CQUFJL0MsV0FBVTtvQkFBMEJnRCxNQUFLO29CQUFlQyxTQUFROzhCQUNuRSw0RUFBQ0M7d0JBQUtDLFVBQVM7d0JBQVVDLEdBQUU7d0JBQW9OQyxVQUFTOzs7Ozs7Ozs7OztZQUc5UCxLQUFLO2dCQUNILHFCQUNFLDhEQUFDTjtvQkFBSS9DLFdBQVU7b0JBQTBCZ0QsTUFBSztvQkFBZUMsU0FBUTs4QkFDbkUsNEVBQUNDO3dCQUFLQyxVQUFTO3dCQUFVQyxHQUFFO3dCQUFvTkMsVUFBUzs7Ozs7Ozs7Ozs7WUFHOVAsS0FBSztnQkFDSCxxQkFDRSw4REFBQ047b0JBQUkvQyxXQUFVO29CQUF1QmdELE1BQUs7b0JBQWVDLFNBQVE7OEJBQ2hFLDRFQUFDQzt3QkFBS0MsVUFBUzt3QkFBVUMsR0FBRTt3QkFBME5DLFVBQVM7Ozs7Ozs7Ozs7O1FBR3RRO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEIsT0FBUXJEO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1zRCxpQkFBaUI7UUFDckIsT0FBUXREO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU11RCxzQkFBc0I7UUFDMUIsT0FBUXZEO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzZDO1FBQUk5QyxXQUFXLENBQUMsa0JBQWtCLEVBQUVBLFVBQVUsQ0FBQzs7MEJBQzlDLDhEQUFDOEM7Z0JBQUk5QyxXQUFVOzBCQUNaNkM7Ozs7OzswQkFFSCw4REFBQ0M7Z0JBQUk5QyxXQUFVOztrQ0FDYiw4REFBQzhDO3dCQUFJOUMsV0FBVyxDQUFDLG9CQUFvQixFQUFFdUQsaUJBQWlCLENBQUM7a0NBQ3RERDs7Ozs7O29CQUVGdkQsZUFBZUkseUJBQ2QsOERBQUMyQzt3QkFBSTlDLFdBQVU7a0NBQ1pHOzs7Ozs7b0JBR0pKLGVBQWVRLGdDQUNkLDhEQUFDdUM7d0JBQUk5QyxXQUFVOzs0QkFBNkI7NEJBQzdCTzs7Ozs7OztvQkFHaEJSLGVBQWVNLDJCQUNkLDhEQUFDeUM7d0JBQUk5QyxXQUFVOzs0QkFBNkI7NEJBQzNCSyxVQUFVb0Qsa0JBQWtCOzs7Ozs7O29CQUc5QzFELGVBQWV5RCx1Q0FDZCw4REFBQ1Y7d0JBQUk5QyxXQUFVO2tDQUNad0Q7Ozs7Ozs7Ozs7OzswQkFJUCw4REFBQ0U7Z0JBQ0NDLFNBQVNoRDtnQkFDVGlELFVBQVUzRCxXQUFXO2dCQUNyQkQsV0FBVTtnQkFDVjZELE9BQU07MEJBRUw1RCxXQUFXLGFBQWEsZ0JBQWdCOzs7Ozs7Ozs7Ozs7QUFJakQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kaWRvLWRpc3RyaWJ1dGlvbi1mcm9udGVuZC8uL2NvbXBvbmVudHMvTmV0d29ya1N0YXR1c0NoZWNrZXIudHN4PzQwMGMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5cclxuaW50ZXJmYWNlIE5ldHdvcmtTdGF0dXNDaGVja2VyUHJvcHMge1xyXG4gIG9uU3RhdHVzQ2hhbmdlPzogKHN0YXR1czogJ2NoZWNraW5nJyB8ICdnb29kJyB8ICdzbG93JyB8ICd1bnN0YWJsZScgfCAnb2ZmbGluZScpID0+IHZvaWQ7XHJcbiAgc2hvd0RldGFpbHM/OiBib29sZWFuO1xyXG4gIGNsYXNzTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmV0d29ya1N0YXR1c0NoZWNrZXIoeyBcclxuICBvblN0YXR1c0NoYW5nZSwgXHJcbiAgc2hvd0RldGFpbHMgPSBmYWxzZSwgXHJcbiAgY2xhc3NOYW1lID0gXCJcIiBcclxufTogTmV0d29ya1N0YXR1c0NoZWNrZXJQcm9wcykge1xyXG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZTwnY2hlY2tpbmcnIHwgJ2dvb2QnIHwgJ3Nsb3cnIHwgJ3Vuc3RhYmxlJyB8ICdvZmZsaW5lJz4oJ2NoZWNraW5nJyk7XHJcbiAgY29uc3QgW2RldGFpbHMsIHNldERldGFpbHNdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XHJcbiAgY29uc3QgW2xhc3RDaGVjaywgc2V0TGFzdENoZWNrXSA9IHVzZVN0YXRlPERhdGUgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbY29ubmVjdGlvblR5cGUsIHNldENvbm5lY3Rpb25UeXBlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xyXG4gIGNvbnN0IFtjaGVja1RpbWVvdXQsIHNldENoZWNrVGltZW91dF0gPSB1c2VTdGF0ZTxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xyXG5cclxuICBjb25zdCBjaGVja05ldHdvcmtTdGF0dXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRTdGF0dXMoJ2NoZWNraW5nJyk7XHJcbiAgICBzZXREZXRhaWxzKCdDaGVja2luZyBuZXR3b3JrIGNvbm5lY3Rpdml0eS4uLicpO1xyXG4gICAgXHJcbiAgICAvLyBDbGVhciBhbnkgZXhpc3RpbmcgdGltZW91dFxyXG4gICAgaWYgKGNoZWNrVGltZW91dCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoY2hlY2tUaW1lb3V0KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gU2V0IGEgdGltZW91dCBmb3IgdGhlIGNoZWNrXHJcbiAgICBjb25zdCB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIHNldFN0YXR1cygnb2ZmbGluZScpO1xyXG4gICAgICBzZXREZXRhaWxzKCdOZXR3b3JrIGNoZWNrIHRpbWVkIG91dCBhZnRlciAxNSBzZWNvbmRzLiBObyBpbnRlcm5ldCBjb25uZWN0aW9uIGRldGVjdGVkLicpO1xyXG4gICAgICBvblN0YXR1c0NoYW5nZT8uKCdvZmZsaW5lJyk7XHJcbiAgICB9LCAxNTAwMCk7XHJcbiAgICBcclxuICAgIHNldENoZWNrVGltZW91dCh0aW1lb3V0KTtcclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQ2hlY2sgY29ubmVjdGlvbiB0eXBlXHJcbiAgICAgIGlmICgnY29ubmVjdGlvbicgaW4gbmF2aWdhdG9yKSB7XHJcbiAgICAgICAgY29uc3QgY29ubiA9IChuYXZpZ2F0b3IgYXMgYW55KS5jb25uZWN0aW9uO1xyXG4gICAgICAgIGlmIChjb25uKSB7XHJcbiAgICAgICAgICBzZXRDb25uZWN0aW9uVHlwZShgJHtjb25uLmVmZmVjdGl2ZVR5cGUgfHwgJ3Vua25vd24nfSAoJHtjb25uLnR5cGUgfHwgJ3Vua25vd24nfSlgKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFRlc3QgY29ubmVjdGlvbiBzcGVlZCBieSBtYWtpbmcgbXVsdGlwbGUgcmVxdWVzdHMgd2l0aCBzaG9ydGVyIHRpbWVvdXRzXHJcbiAgICAgIGNvbnN0IHRlc3RVcmxzID0gW1xyXG4gICAgICAgICdodHRwczovL3d3dy5nb29nbGUuY29tL2Zhdmljb24uaWNvJyxcclxuICAgICAgICAnaHR0cHM6Ly93d3cuY2xvdWRmbGFyZS5jb20vZmF2aWNvbi5pY28nLFxyXG4gICAgICAgICdodHRwczovL3d3dy5naXRodWIuY29tL2Zhdmljb24uaWNvJ1xyXG4gICAgICBdO1xyXG5cclxuICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChcclxuICAgICAgICB0ZXN0VXJscy5tYXAodXJsID0+IFxyXG4gICAgICAgICAgZmV0Y2godXJsLCB7IFxyXG4gICAgICAgICAgICBtZXRob2Q6ICdIRUFEJywgXHJcbiAgICAgICAgICAgIG1vZGU6ICduby1jb3JzJyxcclxuICAgICAgICAgICAgY2FjaGU6ICduby1jYWNoZScsXHJcbiAgICAgICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCg1MDAwKSAvLyA1IHNlY29uZCB0aW1lb3V0IHBlciByZXF1ZXN0XHJcbiAgICAgICAgICB9KVxyXG4gICAgICAgIClcclxuICAgICAgKTtcclxuXHJcbiAgICAgIGNvbnN0IHN1Y2Nlc3NmdWxSZXF1ZXN0cyA9IHJlc3VsdHMuZmlsdGVyKHJlc3VsdCA9PiByZXN1bHQuc3RhdHVzID09PSAnZnVsZmlsbGVkJykubGVuZ3RoO1xyXG4gICAgICBjb25zdCB0b3RhbFJlcXVlc3RzID0gcmVzdWx0cy5sZW5ndGg7XHJcbiAgICAgIFxyXG4gICAgICAvLyBDbGVhciB0aGUgdGltZW91dCBzaW5jZSB3ZSBnb3QgcmVzdWx0c1xyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dCk7XHJcbiAgICAgIHNldENoZWNrVGltZW91dChudWxsKTtcclxuICAgICAgXHJcbiAgICAgIHNldExhc3RDaGVjayhuZXcgRGF0ZSgpKTtcclxuICAgICAgXHJcbiAgICAgIGlmIChzdWNjZXNzZnVsUmVxdWVzdHMgPT09IDApIHtcclxuICAgICAgICBzZXRTdGF0dXMoJ29mZmxpbmUnKTtcclxuICAgICAgICBzZXREZXRhaWxzKCdObyBpbnRlcm5ldCBjb25uZWN0aW9uIGRldGVjdGVkLiBQbGVhc2UgY2hlY2sgeW91ciBuZXR3b3JrIGNvbm5lY3Rpb24uJyk7XHJcbiAgICAgICAgb25TdGF0dXNDaGFuZ2U/Lignb2ZmbGluZScpO1xyXG4gICAgICB9IGVsc2UgaWYgKHN1Y2Nlc3NmdWxSZXF1ZXN0cyA8IHRvdGFsUmVxdWVzdHMpIHtcclxuICAgICAgICBzZXRTdGF0dXMoJ3Vuc3RhYmxlJyk7XHJcbiAgICAgICAgc2V0RGV0YWlscyhgVW5zdGFibGUgY29ubmVjdGlvbjogJHtzdWNjZXNzZnVsUmVxdWVzdHN9LyR7dG90YWxSZXF1ZXN0c30gcmVxdWVzdHMgc3VjY2Vzc2Z1bC4gU29tZSByZXF1ZXN0cyBmYWlsZWQuYCk7XHJcbiAgICAgICAgb25TdGF0dXNDaGFuZ2U/LigndW5zdGFibGUnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyBBbGwgcmVxdWVzdHMgc3VjY2Vzc2Z1bCwgbm93IHRlc3Qgc3BlZWRcclxuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBhd2FpdCBmZXRjaCgnaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS9mYXZpY29uLmljbycsIHsgXHJcbiAgICAgICAgICAgIG1ldGhvZDogJ0hFQUQnLCBcclxuICAgICAgICAgICAgbW9kZTogJ25vLWNvcnMnLFxyXG4gICAgICAgICAgICBjYWNoZTogJ25vLWNhY2hlJyxcclxuICAgICAgICAgICAgc2lnbmFsOiBBYm9ydFNpZ25hbC50aW1lb3V0KDMwMDApIC8vIDMgc2Vjb25kIHRpbWVvdXQgZm9yIHNwZWVkIHRlc3RcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XHJcbiAgICAgICAgICBjb25zdCByZXNwb25zZVRpbWUgPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAocmVzcG9uc2VUaW1lIDwgMTAwMCkge1xyXG4gICAgICAgICAgICBzZXRTdGF0dXMoJ2dvb2QnKTtcclxuICAgICAgICAgICAgc2V0RGV0YWlscyhgR29vZCBjb25uZWN0aW9uOiAke3Jlc3BvbnNlVGltZX1tcyByZXNwb25zZSB0aW1lYCk7XHJcbiAgICAgICAgICAgIG9uU3RhdHVzQ2hhbmdlPy4oJ2dvb2QnKTtcclxuICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2VUaW1lIDwgMzAwMCkge1xyXG4gICAgICAgICAgICBzZXRTdGF0dXMoJ3Nsb3cnKTtcclxuICAgICAgICAgICAgc2V0RGV0YWlscyhgU2xvdyBjb25uZWN0aW9uOiAke3Jlc3BvbnNlVGltZX1tcyByZXNwb25zZSB0aW1lYCk7XHJcbiAgICAgICAgICAgIG9uU3RhdHVzQ2hhbmdlPy4oJ3Nsb3cnKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldFN0YXR1cygndW5zdGFibGUnKTtcclxuICAgICAgICAgICAgc2V0RGV0YWlscyhgVmVyeSBzbG93IGNvbm5lY3Rpb246ICR7cmVzcG9uc2VUaW1lfW1zIHJlc3BvbnNlIHRpbWVgKTtcclxuICAgICAgICAgICAgb25TdGF0dXNDaGFuZ2U/LigndW5zdGFibGUnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgc2V0U3RhdHVzKCd1bnN0YWJsZScpO1xyXG4gICAgICAgICAgc2V0RGV0YWlscygnQ29ubmVjdGlvbiB0ZXN0IGZhaWxlZCcpO1xyXG4gICAgICAgICAgb25TdGF0dXNDaGFuZ2U/LigndW5zdGFibGUnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIC8vIENsZWFyIHRoZSB0aW1lb3V0IHNpbmNlIHdlIGdvdCBhbiBlcnJvclxyXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dCk7XHJcbiAgICAgIHNldENoZWNrVGltZW91dChudWxsKTtcclxuICAgICAgXHJcbiAgICAgIHNldExhc3RDaGVjayhuZXcgRGF0ZSgpKTtcclxuICAgICAgc2V0U3RhdHVzKCdvZmZsaW5lJyk7XHJcbiAgICAgIHNldERldGFpbHMoYE5ldHdvcmsgY2hlY2sgZmFpbGVkOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWApO1xyXG4gICAgICBvblN0YXR1c0NoYW5nZT8uKCdvZmZsaW5lJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNoZWNrTmV0d29ya1N0YXR1cygpO1xyXG4gICAgXHJcbiAgICAvLyBDbGVhbnVwIHRpbWVvdXQgb24gdW5tb3VudFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgaWYgKGNoZWNrVGltZW91dCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChjaGVja1RpbWVvdXQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgZ2V0U3RhdHVzSWNvbiA9ICgpID0+IHtcclxuICAgIHN3aXRjaCAoc3RhdHVzKSB7XHJcbiAgICAgIGNhc2UgJ2NoZWNraW5nJzpcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cclxuICAgICAgICApO1xyXG4gICAgICBjYXNlICdnb29kJzpcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNjAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2em0zLjcwNy05LjI5M2ExIDEgMCAwMC0xLjQxNC0xLjQxNEw5IDEwLjU4NiA3LjcwNyA5LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNGwyIDJhMSAxIDAgMDAxLjQxNCAwbDQtNHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgKTtcclxuICAgICAgY2FzZSAnc2xvdyc6XHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy02MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxyXG4gICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTguMjU3IDMuMDk5Yy43NjUtMS4zNiAyLjcyMi0xLjM2IDMuNDg2IDBsNS41OCA5LjkyYy43NSAxLjMzNC0uMjEzIDIuOTgtMS43NDIgMi45OEg0LjQyYy0xLjUzIDAtMi40OTMtMS42NDYtMS43NDMtMi45OGw1LjU4LTkuOTJ6TTExIDEzYTEgMSAwIDExLTIgMCAxIDEgMCAwMTIgMHptLTEtOGExIDEgMCAwMC0xIDF2M2ExIDEgMCAwMDIgMFY2YTEgMSAwIDAwLTEtMXpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgKTtcclxuICAgICAgY2FzZSAndW5zdGFibGUnOlxyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1vcmFuZ2UtNjAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk04LjI1NyAzLjA5OWMuNzY1LTEuMzYgMi43MjItMS4zNiAzLjQ4NiAwbDUuNTggOS45MmMuNzUgMS4zMzQtLjIxMyAyLjk4LTEuNzQyIDIuOThINC40MmMtMS41MyAwLTIuNDkzLTEuNjQ2LTEuNzQzLTIuOThsNS41OC05Ljkyek0xMSAxM2ExIDEgMCAxMS0yIDAgMSAxIDAgMDEyIDB6bS0xLThhMSAxIDAgMDAtMSAxdjNhMSAxIDAgMDAyIDBWNmExIDEgMCAwMC0xLTF6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICk7XHJcbiAgICAgIGNhc2UgJ29mZmxpbmUnOlxyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNjAwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cclxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2ek04LjcwNyA3LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNEw4LjU4NiAxMGwtMS4yOTMgMS4yOTNhMSAxIDAgMTAxLjQxNCAxLjQxNEwxMCAxMS40MTRsMS4yOTMgMS4yOTNhMSAxIDAgMDAxLjQxNC0xLjQxNEwxMS40MTQgMTBsMS4yOTMtMS4yOTNhMSAxIDAgMDAtMS40MTQtMS40MTRMMTAgOC41ODYgOC43MDcgNy4yOTN6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgZ2V0U3RhdHVzVGV4dCA9ICgpID0+IHtcclxuICAgIHN3aXRjaCAoc3RhdHVzKSB7XHJcbiAgICAgIGNhc2UgJ2NoZWNraW5nJzpcclxuICAgICAgICByZXR1cm4gJ0NoZWNraW5nIG5ldHdvcmsgY29ubmVjdGlvbi4uLic7XHJcbiAgICAgIGNhc2UgJ2dvb2QnOlxyXG4gICAgICAgIHJldHVybiAnTmV0d29yayBjb25uZWN0aW9uIGlzIGdvb2QnO1xyXG4gICAgICBjYXNlICdzbG93JzpcclxuICAgICAgICByZXR1cm4gJ05ldHdvcmsgY29ubmVjdGlvbiBpcyBzbG93JztcclxuICAgICAgY2FzZSAndW5zdGFibGUnOlxyXG4gICAgICAgIHJldHVybiAnTmV0d29yayBjb25uZWN0aW9uIGlzIHVuc3RhYmxlJztcclxuICAgICAgY2FzZSAnb2ZmbGluZSc6XHJcbiAgICAgICAgcmV0dXJuICdObyBuZXR3b3JrIGNvbm5lY3Rpb24nO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKCkgPT4ge1xyXG4gICAgc3dpdGNoIChzdGF0dXMpIHtcclxuICAgICAgY2FzZSAnY2hlY2tpbmcnOlxyXG4gICAgICAgIHJldHVybiAndGV4dC1ibHVlLTYwMCc7XHJcbiAgICAgIGNhc2UgJ2dvb2QnOlxyXG4gICAgICAgIHJldHVybiAndGV4dC1ncmVlbi02MDAnO1xyXG4gICAgICBjYXNlICdzbG93JzpcclxuICAgICAgICByZXR1cm4gJ3RleHQteWVsbG93LTYwMCc7XHJcbiAgICAgIGNhc2UgJ3Vuc3RhYmxlJzpcclxuICAgICAgICByZXR1cm4gJ3RleHQtb3JhbmdlLTYwMCc7XHJcbiAgICAgIGNhc2UgJ29mZmxpbmUnOlxyXG4gICAgICAgIHJldHVybiAndGV4dC1yZWQtNjAwJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBnZXRDb25uZWN0aW9uQWR2aWNlID0gKCkgPT4ge1xyXG4gICAgc3dpdGNoIChzdGF0dXMpIHtcclxuICAgICAgY2FzZSAnZ29vZCc6XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgIGNhc2UgJ3Nsb3cnOlxyXG4gICAgICAgIHJldHVybiAnWW91ciBjb25uZWN0aW9uIGlzIHNsb3cuIEF1dGhlbnRpY2F0aW9uIG1heSB0YWtlIGxvbmdlciB0aGFuIHVzdWFsLic7XHJcbiAgICAgIGNhc2UgJ3Vuc3RhYmxlJzpcclxuICAgICAgICByZXR1cm4gJ1lvdXIgY29ubmVjdGlvbiBpcyB1bnN0YWJsZS4gQ29uc2lkZXIgdXNpbmcgYSB3aXJlZCBjb25uZWN0aW9uIG9yIG1vdmluZyBjbG9zZXIgdG8geW91ciByb3V0ZXIuJztcclxuICAgICAgY2FzZSAnb2ZmbGluZSc6XHJcbiAgICAgICAgcmV0dXJuICdObyBpbnRlcm5ldCBjb25uZWN0aW9uIGRldGVjdGVkLiBQbGVhc2UgY2hlY2sgeW91ciBuZXR3b3JrIHNldHRpbmdzLic7XHJcbiAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgJHtjbGFzc05hbWV9YH0+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXItMlwiPlxyXG4gICAgICAgIHtnZXRTdGF0dXNJY29uKCl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldFN0YXR1c0NvbG9yKCl9YH0+XHJcbiAgICAgICAgICB7Z2V0U3RhdHVzVGV4dCgpfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIHtzaG93RGV0YWlscyAmJiBkZXRhaWxzICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5cclxuICAgICAgICAgICAge2RldGFpbHN9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHtzaG93RGV0YWlscyAmJiBjb25uZWN0aW9uVHlwZSAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+XHJcbiAgICAgICAgICAgIENvbm5lY3Rpb246IHtjb25uZWN0aW9uVHlwZX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgICAge3Nob3dEZXRhaWxzICYmIGxhc3RDaGVjayAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+XHJcbiAgICAgICAgICAgIExhc3QgY2hlY2tlZDoge2xhc3RDaGVjay50b0xvY2FsZVRpbWVTdHJpbmcoKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgICAge3Nob3dEZXRhaWxzICYmIGdldENvbm5lY3Rpb25BZHZpY2UoKSAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIG10LTEgZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAge2dldENvbm5lY3Rpb25BZHZpY2UoKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8YnV0dG9uXHJcbiAgICAgICAgb25DbGljaz17Y2hlY2tOZXR3b3JrU3RhdHVzfVxyXG4gICAgICAgIGRpc2FibGVkPXtzdGF0dXMgPT09ICdjaGVja2luZyd9XHJcbiAgICAgICAgY2xhc3NOYW1lPVwibWwtMiBweC0yIHB5LTEgdGV4dC14cyBiZy1ncmF5LTEwMCBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkIHRyYW5zaXRpb24gZGlzYWJsZWQ6b3BhY2l0eS01MFwiXHJcbiAgICAgICAgdGl0bGU9XCJSZWZyZXNoIG5ldHdvcmsgc3RhdHVzXCJcclxuICAgICAgPlxyXG4gICAgICAgIHtzdGF0dXMgPT09ICdjaGVja2luZycgPyAnQ2hlY2tpbmcuLi4nIDogJ1JlZnJlc2gnfVxyXG4gICAgICA8L2J1dHRvbj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0gIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJOZXR3b3JrU3RhdHVzQ2hlY2tlciIsIm9uU3RhdHVzQ2hhbmdlIiwic2hvd0RldGFpbHMiLCJjbGFzc05hbWUiLCJzdGF0dXMiLCJzZXRTdGF0dXMiLCJkZXRhaWxzIiwic2V0RGV0YWlscyIsImxhc3RDaGVjayIsInNldExhc3RDaGVjayIsImNvbm5lY3Rpb25UeXBlIiwic2V0Q29ubmVjdGlvblR5cGUiLCJjaGVja1RpbWVvdXQiLCJzZXRDaGVja1RpbWVvdXQiLCJjaGVja05ldHdvcmtTdGF0dXMiLCJjbGVhclRpbWVvdXQiLCJ0aW1lb3V0Iiwic2V0VGltZW91dCIsIm5hdmlnYXRvciIsImNvbm4iLCJjb25uZWN0aW9uIiwiZWZmZWN0aXZlVHlwZSIsInR5cGUiLCJ0ZXN0VXJscyIsInJlc3VsdHMiLCJQcm9taXNlIiwiYWxsU2V0dGxlZCIsIm1hcCIsInVybCIsImZldGNoIiwibWV0aG9kIiwibW9kZSIsImNhY2hlIiwic2lnbmFsIiwiQWJvcnRTaWduYWwiLCJzdWNjZXNzZnVsUmVxdWVzdHMiLCJmaWx0ZXIiLCJyZXN1bHQiLCJsZW5ndGgiLCJ0b3RhbFJlcXVlc3RzIiwiRGF0ZSIsInN0YXJ0VGltZSIsIm5vdyIsImVuZFRpbWUiLCJyZXNwb25zZVRpbWUiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsImdldFN0YXR1c0ljb24iLCJkaXYiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsImdldFN0YXR1c1RleHQiLCJnZXRTdGF0dXNDb2xvciIsImdldENvbm5lY3Rpb25BZHZpY2UiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJ0aXRsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/NetworkStatusChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [fetchingWarehouse, setFetchingWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshingToken, setRefreshingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use refs to avoid dependency issues\n    const fetchingWarehouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const tokenRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const userRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update refs when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchingWarehouseRef.current = fetchingWarehouse;\n    }, [\n        fetchingWarehouse\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        tokenRef.current = token;\n    }, [\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        userRef.current = user;\n    }, [\n        user\n    ]);\n    // Helper to fetch warehouse info in background\n    const fetchAndPersistWarehouseInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userObjParam, tokenParam)=>{\n        const tokenToUse = tokenParam !== undefined ? tokenParam : tokenRef.current;\n        const userToFetch = userObjParam || userRef.current;\n        console.log(\"fetchAndPersistWarehouseInfo called with:\", {\n            tokenToUse: !!tokenToUse,\n            userToFetch: userToFetch?.uuid,\n            userToFetchWarehouseUuid: userToFetch?.warehouseUuidString || userToFetch?.warehouseUuid,\n            fetchingWarehouse: fetchingWarehouseRef.current,\n            currentUser: userRef.current?.uuid,\n            currentUserWarehouseUuid: userRef.current?.warehouseUuidString || userRef.current?.warehouseUuid\n        });\n        if (!tokenToUse || !userToFetch?.uuid || fetchingWarehouseRef.current) {\n            console.log(\"fetchAndPersistWarehouseInfo early return:\", {\n                noToken: !tokenToUse,\n                noUserUuid: !userToFetch?.uuid,\n                alreadyFetching: fetchingWarehouseRef.current\n            });\n            return;\n        }\n        // Additional safety check: ensure UUID is a valid string\n        if (typeof userToFetch.uuid !== \"string\" || userToFetch.uuid.trim() === \"\") {\n            console.log(\"fetchAndPersistWarehouseInfo early return: invalid UUID:\", userToFetch.uuid);\n            return;\n        }\n        // Additional check: if we don't have a valid token in localStorage, don't proceed\n        const storedToken = localStorage.getItem(\"dido_token\");\n        if (!storedToken) {\n            console.log(\"fetchAndPersistWarehouseInfo early return: no stored token\");\n            return;\n        }\n        setFetchingWarehouse(true);\n        try {\n            console.log(\"Fetching latest user info for UUID:\", userToFetch.uuid);\n            // Always fetch the latest user info from backend to ensure we have current data\n            const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!userRes.ok) {\n                console.error(\"Failed to fetch user info:\", userRes.status, userRes.statusText);\n                return;\n            }\n            const latestUser = await userRes.json();\n            console.log(\"Latest user info from API:\", latestUser);\n            console.log(\"Warehouse UUID from backend:\", {\n                warehouseUuidString: latestUser.warehouseUuidString,\n                warehouseUuid: latestUser.warehouseUuid,\n                finalWarehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid\n            });\n            // Check if user has a warehouse assigned\n            const warehouseUuid = latestUser.warehouseUuidString || latestUser.warehouseUuid;\n            if (!warehouseUuid) {\n                console.log(\"No warehouse assigned to user, setting user without warehouse info\");\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: null,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            console.log(\"Fetching warehouse info for UUID:\", warehouseUuid);\n            const warehouseRes = await fetch(`/api/warehouses/${warehouseUuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!warehouseRes.ok) {\n                console.error(\"Failed to fetch warehouse info:\", warehouseRes.status, warehouseRes.statusText);\n                // If warehouse fetch fails, still update user with current data but without warehouse name\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: warehouseUuid,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            const warehouse = await warehouseRes.json();\n            console.log(\"Warehouse info from API:\", warehouse);\n            const updatedUser = {\n                ...latestUser,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouse.name\n            };\n            console.log(\"Setting updated user with warehouse info:\", updatedUser);\n            setUser(updatedUser);\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n        } catch (err) {\n            console.error(\"Error fetching warehouse info:\", err);\n            // On error, still try to update user with current data from backend\n            try {\n                const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                    headers: {\n                        Authorization: `Bearer ${tokenToUse}`\n                    }\n                });\n                if (userRes.ok) {\n                    const latestUser = await userRes.json();\n                    const updatedUser = {\n                        ...latestUser,\n                        warehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid,\n                        warehouseName: null\n                    };\n                    setUser(updatedUser);\n                    localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                }\n            } catch (fallbackErr) {\n                console.error(\"Error in fallback user update:\", fallbackErr);\n            }\n        } finally{\n            setFetchingWarehouse(false);\n        }\n    }, []); // Remove all dependencies since we're using refs\n    // Refresh access token using refresh token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!refreshToken || refreshingToken) return false;\n        setRefreshingToken(true);\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken,\n                    clientIp: \"frontend\",\n                    userAgent: navigator.userAgent\n                })\n            });\n            if (!response.ok) {\n                console.error(\"Token refresh failed:\", response.status);\n                return false;\n            }\n            const data = await response.json();\n            setToken(data.accessToken);\n            setRefreshToken(data.refreshToken);\n            localStorage.setItem(\"dido_token\", data.accessToken);\n            localStorage.setItem(\"dido_refresh_token\", data.refreshToken);\n            console.log(\"Token refreshed successfully\");\n            return true;\n        } catch (error) {\n            console.error(\"Error refreshing token:\", error);\n            return false;\n        } finally{\n            setRefreshingToken(false);\n        }\n    }, [\n        refreshToken,\n        refreshingToken\n    ]);\n    // Get security status for the current user\n    const getSecurityStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!token) return null;\n        try {\n            const response = await fetch(\"/api/auth/security-status\", {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (!response.ok) {\n                console.error(\"Failed to get security status:\", response.status);\n                return null;\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"Error getting security status:\", error);\n            return null;\n        }\n    }, [\n        token\n    ]);\n    // On initial mount: restore session from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext initial mount - checking localStorage\");\n        const storedUser = localStorage.getItem(\"dido_user\");\n        const storedToken = localStorage.getItem(\"dido_token\");\n        const storedRefreshToken = localStorage.getItem(\"dido_refresh_token\");\n        console.log(\"Stored data from localStorage:\", {\n            hasStoredUser: !!storedUser,\n            hasStoredToken: !!storedToken,\n            hasStoredRefreshToken: !!storedRefreshToken\n        });\n        if (storedUser && storedToken) {\n            const userObj = JSON.parse(storedUser);\n            console.log(\"Restored user from localStorage:\", {\n                userUuid: userObj.uuid,\n                userEmail: userObj.email,\n                userWarehouseUuid: userObj.warehouseUuidString || userObj.warehouseUuid,\n                userWarehouseName: userObj.warehouseName\n            });\n            // Validate that the user UUID is still valid by fetching from backend\n            const validateUserExists = async ()=>{\n                try {\n                    console.log(\"Validating user UUID from localStorage:\", userObj.uuid);\n                    const response = await fetch(`/api/users/${userObj.uuid}`, {\n                        headers: {\n                            Authorization: `Bearer ${storedToken}`\n                        }\n                    });\n                    if (response.status === 404) {\n                        console.log(\"User UUID not found in backend (404), clearing localStorage and redirecting to login\");\n                        // User was deleted/recreated, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    if (!response.ok) {\n                        console.log(\"Failed to validate user UUID:\", response.status, response.statusText);\n                        // If we can't validate the user, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    // User exists, proceed with normal flow\n                    console.log(\"User UUID validated successfully\");\n                    setUser(userObj);\n                    setToken(storedToken);\n                    if (storedRefreshToken) {\n                        setRefreshToken(storedRefreshToken);\n                    }\n                    // Always trigger background fetch to ensure we have the latest warehouse info\n                    // This is especially important after database clearing when warehouse UUIDs may have changed\n                    console.log(\"Triggering background warehouse info fetch to ensure latest data from localStorage\");\n                    setTimeout(()=>{\n                        fetchAndPersistWarehouseInfo(userObj, storedToken);\n                    }, 0);\n                } catch (error) {\n                    console.error(\"Error validating user UUID:\", error);\n                    // If there's a network error, clear localStorage and redirect to login\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                }\n            };\n            // Validate user exists before setting state\n            validateUserExists();\n        } else {\n            console.log(\"No stored user/token found in localStorage\");\n        }\n        // If we have no valid authentication and we're not on the auth page, redirect\n        if (!storedToken && !window.location.pathname.includes(\"/auth\")) {\n            console.log(\"No valid authentication found, redirecting to login\");\n            router.replace(\"/auth\");\n        }\n        setLoading(false);\n    }, []); // Remove fetchAndPersistWarehouseInfo from dependencies to prevent infinite loop\n    // Periodically validate session and refresh token if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!token || !refreshToken) {\n            console.log(\"No token or refresh token available, skipping periodic validation\");\n            return;\n        }\n        const interval = setInterval(async ()=>{\n            try {\n                console.log(\"Periodic token validation - checking token validity\");\n                // Try to validate current token\n                const res = await fetch(\"/api/auth/validate\", {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                });\n                if (!res.ok) {\n                    console.log(\"Token validation failed, attempting refresh\");\n                    // Token is invalid, try to refresh\n                    const refreshSuccess = await refreshAccessToken();\n                    if (!refreshSuccess) {\n                        console.log(\"Token refresh failed, logging out user\");\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                    } else {\n                        console.log(\"Token refresh successful\");\n                    }\n                } else {\n                    console.log(\"Token validation successful\");\n                }\n            } catch (error) {\n                console.log(\"Network error during token validation, attempting refresh\");\n                // Network error, try to refresh token\n                const refreshSuccess = await refreshAccessToken();\n                if (!refreshSuccess) {\n                    console.log(\"Token refresh failed after network error, logging out user\");\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                } else {\n                    console.log(\"Token refresh successful after network error\");\n                }\n            }\n        }, 30 * 60 * 1000); // 30 minutes\n        return ()=>clearInterval(interval);\n    }, [\n        token,\n        refreshToken,\n        refreshAccessToken,\n        router\n    ]);\n    const login = (user, token, refreshTokenValue)=>{\n        console.log(\"AuthContext login called with:\", {\n            userUuid: user.uuid,\n            userEmail: user.email,\n            userWarehouseUuid: user.warehouseUuidString || user.warehouseUuid,\n            userWarehouseUuidString: user.warehouseUuidString,\n            userWarehouseUuidLegacy: user.warehouseUuid,\n            userWarehouseName: user.warehouseName,\n            hasToken: !!token,\n            hasRefreshToken: !!refreshTokenValue\n        });\n        setUser(user);\n        setToken(token);\n        setRefreshToken(refreshTokenValue);\n        localStorage.setItem(\"dido_user\", JSON.stringify(user));\n        localStorage.setItem(\"dido_token\", token);\n        localStorage.setItem(\"dido_refresh_token\", refreshTokenValue);\n        // Always trigger background fetch to ensure we have the latest warehouse info\n        // This is especially important after database clearing when warehouse UUIDs may have changed\n        console.log(\"Triggering background warehouse info fetch to ensure latest data\");\n        setTimeout(()=>{\n            fetchAndPersistWarehouseInfo(user, token);\n        }, 0);\n    };\n    const logout = async ()=>{\n        console.log(\"AuthContext logout called - clearing session and redirecting to /auth\");\n        // Revoke tokens on backend if we have them\n        if (token && refreshToken) {\n            try {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${token}`\n                    },\n                    body: JSON.stringify({\n                        refreshToken,\n                        revokeAll: false\n                    })\n                });\n            } catch (error) {\n                console.error(\"Error during logout:\", error);\n            // Continue with logout even if backend call fails\n            }\n        }\n        setUser(null);\n        setToken(null);\n        setRefreshToken(null);\n        localStorage.removeItem(\"dido_token\");\n        localStorage.removeItem(\"dido_refresh_token\");\n        localStorage.removeItem(\"dido_user\");\n        console.log(\"Session cleared, redirecting to /auth\");\n        router.replace(\"/auth\");\n    };\n    const checkUserExists = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (uuid)=>{\n        if (!token) return false;\n        // Safety check: ensure UUID is valid\n        if (!uuid || typeof uuid !== \"string\" || uuid.trim() === \"\") {\n            console.log(\"checkUserExists: invalid UUID provided:\", uuid);\n            return false;\n        }\n        try {\n            const response = await fetch(`/api/users/${uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.status === 404) return false;\n            if (!response.ok) throw new Error(\"Failed to check user existence\");\n            return true;\n        } catch (error) {\n            console.error(\"Error checking user existence:\", error);\n            return false;\n        }\n    }, [\n        token\n    ]);\n    const switchWarehouse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (warehouseUuid, warehouseName)=>{\n        if (!user?.uuid || !token) {\n            throw new Error(\"User not authenticated\");\n        }\n        // Safety check: ensure user UUID is valid\n        if (typeof user.uuid !== \"string\" || user.uuid.trim() === \"\") {\n            throw new Error(\"Invalid user UUID\");\n        }\n        try {\n            // Update user's warehouse on backend\n            const response = await fetch(`/api/users/${user.uuid}/warehouse`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    warehouseUuid\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update warehouse on backend\");\n            }\n            // Update user context with new warehouse info\n            const updatedUser = {\n                ...user,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouseName\n            };\n            // Update localStorage\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n            // Update context state\n            setUser(updatedUser);\n            console.log(\"Warehouse switched successfully:\", {\n                warehouseUuid,\n                warehouseName\n            });\n        } catch (error) {\n            console.error(\"Error switching warehouse:\", error);\n            throw error;\n        }\n    }, [\n        user,\n        token\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            refreshToken,\n            loading,\n            login,\n            logout,\n            refreshAccessToken,\n            getSecurityStatus,\n            fetchAndPersistWarehouseInfo,\n            checkUserExists,\n            switchWarehouse\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDOEc7QUFDbEU7QUFnRTVDLE1BQU1RLDRCQUFjUCxvREFBYUEsQ0FBOEJRO0FBRXhELFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUEyQjtJQUNoRSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1YsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNhLGNBQWNDLGdCQUFnQixHQUFHZCwrQ0FBUUEsQ0FBZ0I7SUFDaEUsTUFBTSxDQUFDZSxTQUFTQyxXQUFXLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQixtQkFBbUJDLHFCQUFxQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDbUIsaUJBQWlCQyxtQkFBbUIsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU1xQixTQUFTakIsMERBQVNBO0lBRXhCLHNDQUFzQztJQUN0QyxNQUFNa0IsdUJBQXVCbkIsNkNBQU1BLENBQUM7SUFDcEMsTUFBTW9CLFdBQVdwQiw2Q0FBTUEsQ0FBZ0I7SUFDdkMsTUFBTXFCLFVBQVVyQiw2Q0FBTUEsQ0FBYztJQUVwQyxpQ0FBaUM7SUFDakNGLGdEQUFTQSxDQUFDO1FBQ1JxQixxQkFBcUJHLE9BQU8sR0FBR1I7SUFDakMsR0FBRztRQUFDQTtLQUFrQjtJQUV0QmhCLGdEQUFTQSxDQUFDO1FBQ1JzQixTQUFTRSxPQUFPLEdBQUdkO0lBQ3JCLEdBQUc7UUFBQ0E7S0FBTTtJQUVWVixnREFBU0EsQ0FBQztRQUNSdUIsUUFBUUMsT0FBTyxHQUFHaEI7SUFDcEIsR0FBRztRQUFDQTtLQUFLO0lBRVQsK0NBQStDO0lBQy9DLE1BQU1pQiwrQkFBK0J4QixrREFBV0EsQ0FBQyxPQUFPeUIsY0FBcUJDO1FBQzNFLE1BQU1DLGFBQWFELGVBQWV0QixZQUFZc0IsYUFBYUwsU0FBU0UsT0FBTztRQUMzRSxNQUFNSyxjQUFjSCxnQkFBZ0JILFFBQVFDLE9BQU87UUFFbkRNLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkM7WUFDdkRILFlBQVksQ0FBQyxDQUFDQTtZQUNkQyxhQUFhQSxhQUFhRztZQUMxQkMsMEJBQTBCSixhQUFhSyx1QkFBdUJMLGFBQWFNO1lBQzNFbkIsbUJBQW1CSyxxQkFBcUJHLE9BQU87WUFDL0NZLGFBQWFiLFFBQVFDLE9BQU8sRUFBRVE7WUFDOUJLLDBCQUEwQmQsUUFBUUMsT0FBTyxFQUFFVSx1QkFBdUJYLFFBQVFDLE9BQU8sRUFBRVc7UUFDckY7UUFFQSxJQUFJLENBQUNQLGNBQWMsQ0FBQ0MsYUFBYUcsUUFBUVgscUJBQXFCRyxPQUFPLEVBQUU7WUFDckVNLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBOEM7Z0JBQ3hETyxTQUFTLENBQUNWO2dCQUNWVyxZQUFZLENBQUNWLGFBQWFHO2dCQUMxQlEsaUJBQWlCbkIscUJBQXFCRyxPQUFPO1lBQy9DO1lBQ0E7UUFDRjtRQUVBLHlEQUF5RDtRQUN6RCxJQUFJLE9BQU9LLFlBQVlHLElBQUksS0FBSyxZQUFZSCxZQUFZRyxJQUFJLENBQUNTLElBQUksT0FBTyxJQUFJO1lBQzFFWCxRQUFRQyxHQUFHLENBQUMsNERBQTRERixZQUFZRyxJQUFJO1lBQ3hGO1FBQ0Y7UUFFQSxrRkFBa0Y7UUFDbEYsTUFBTVUsY0FBY0MsYUFBYUMsT0FBTyxDQUFDO1FBQ3pDLElBQUksQ0FBQ0YsYUFBYTtZQUNoQlosUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBZCxxQkFBcUI7UUFDckIsSUFBSTtZQUNGYSxRQUFRQyxHQUFHLENBQUMsdUNBQXVDRixZQUFZRyxJQUFJO1lBQ25FLGdGQUFnRjtZQUNoRixNQUFNYSxVQUFVLE1BQU1DLE1BQU0sQ0FBQyxXQUFXLEVBQUVqQixZQUFZRyxJQUFJLENBQUMsQ0FBQyxFQUFFO2dCQUM1RGUsU0FBUztvQkFBRUMsZUFBZSxDQUFDLE9BQU8sRUFBRXBCLFdBQVcsQ0FBQztnQkFBQztZQUNuRDtZQUNBLElBQUksQ0FBQ2lCLFFBQVFJLEVBQUUsRUFBRTtnQkFDZm5CLFFBQVFvQixLQUFLLENBQUMsOEJBQThCTCxRQUFRTSxNQUFNLEVBQUVOLFFBQVFPLFVBQVU7Z0JBQzlFO1lBQ0Y7WUFDQSxNQUFNQyxhQUFhLE1BQU1SLFFBQVFTLElBQUk7WUFDckN4QixRQUFRQyxHQUFHLENBQUMsOEJBQThCc0I7WUFDMUN2QixRQUFRQyxHQUFHLENBQUMsZ0NBQWdDO2dCQUMxQ0cscUJBQXFCbUIsV0FBV25CLG1CQUFtQjtnQkFDbkRDLGVBQWVrQixXQUFXbEIsYUFBYTtnQkFDdkNvQixvQkFBb0JGLFdBQVduQixtQkFBbUIsSUFBSW1CLFdBQVdsQixhQUFhO1lBQ2hGO1lBRUEseUNBQXlDO1lBQ3pDLE1BQU1BLGdCQUFnQmtCLFdBQVduQixtQkFBbUIsSUFBSW1CLFdBQVdsQixhQUFhO1lBQ2hGLElBQUksQ0FBQ0EsZUFBZTtnQkFDbEJMLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixNQUFNeUIsY0FBb0I7b0JBQ3hCLEdBQUdILFVBQVU7b0JBQ2JsQixlQUFlO29CQUNmc0IsZUFBZTtnQkFDakI7Z0JBQ0FoRCxRQUFRK0M7Z0JBQ1JiLGFBQWFlLE9BQU8sQ0FBQyxhQUFhQyxLQUFLQyxTQUFTLENBQUNKO2dCQUNqRDtZQUNGO1lBRUExQixRQUFRQyxHQUFHLENBQUMscUNBQXFDSTtZQUNqRCxNQUFNMEIsZUFBZSxNQUFNZixNQUFNLENBQUMsZ0JBQWdCLEVBQUVYLGNBQWMsQ0FBQyxFQUFFO2dCQUNuRVksU0FBUztvQkFBRUMsZUFBZSxDQUFDLE9BQU8sRUFBRXBCLFdBQVcsQ0FBQztnQkFBQztZQUNuRDtZQUVBLElBQUksQ0FBQ2lDLGFBQWFaLEVBQUUsRUFBRTtnQkFDcEJuQixRQUFRb0IsS0FBSyxDQUFDLG1DQUFtQ1csYUFBYVYsTUFBTSxFQUFFVSxhQUFhVCxVQUFVO2dCQUM3RiwyRkFBMkY7Z0JBQzNGLE1BQU1JLGNBQW9CO29CQUN4QixHQUFHSCxVQUFVO29CQUNibEIsZUFBZUE7b0JBQ2ZzQixlQUFlO2dCQUNqQjtnQkFDQWhELFFBQVErQztnQkFDUmIsYUFBYWUsT0FBTyxDQUFDLGFBQWFDLEtBQUtDLFNBQVMsQ0FBQ0o7Z0JBQ2pEO1lBQ0Y7WUFFQSxNQUFNTSxZQUFZLE1BQU1ELGFBQWFQLElBQUk7WUFDekN4QixRQUFRQyxHQUFHLENBQUMsNEJBQTRCK0I7WUFFeEMsTUFBTU4sY0FBb0I7Z0JBQ3hCLEdBQUdILFVBQVU7Z0JBQ2JsQixlQUFlQTtnQkFDZnNCLGVBQWVLLFVBQVVDLElBQUk7WUFDL0I7WUFFQWpDLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkN5QjtZQUN6RC9DLFFBQVErQztZQUNSYixhQUFhZSxPQUFPLENBQUMsYUFBYUMsS0FBS0MsU0FBUyxDQUFDSjtRQUNuRCxFQUFFLE9BQU9RLEtBQUs7WUFDWmxDLFFBQVFvQixLQUFLLENBQUMsa0NBQWtDYztZQUNoRCxvRUFBb0U7WUFDcEUsSUFBSTtnQkFDRixNQUFNbkIsVUFBVSxNQUFNQyxNQUFNLENBQUMsV0FBVyxFQUFFakIsWUFBWUcsSUFBSSxDQUFDLENBQUMsRUFBRTtvQkFDNURlLFNBQVM7d0JBQUVDLGVBQWUsQ0FBQyxPQUFPLEVBQUVwQixXQUFXLENBQUM7b0JBQUM7Z0JBQ25EO2dCQUNBLElBQUlpQixRQUFRSSxFQUFFLEVBQUU7b0JBQ2QsTUFBTUksYUFBYSxNQUFNUixRQUFRUyxJQUFJO29CQUNyQyxNQUFNRSxjQUFvQjt3QkFDeEIsR0FBR0gsVUFBVTt3QkFDYmxCLGVBQWVrQixXQUFXbkIsbUJBQW1CLElBQUltQixXQUFXbEIsYUFBYTt3QkFDekVzQixlQUFlO29CQUNqQjtvQkFDQWhELFFBQVErQztvQkFDUmIsYUFBYWUsT0FBTyxDQUFDLGFBQWFDLEtBQUtDLFNBQVMsQ0FBQ0o7Z0JBQ25EO1lBQ0YsRUFBRSxPQUFPUyxhQUFhO2dCQUNwQm5DLFFBQVFvQixLQUFLLENBQUMsa0NBQWtDZTtZQUNsRDtRQUNGLFNBQVU7WUFDUmhELHFCQUFxQjtRQUN2QjtJQUNGLEdBQUcsRUFBRSxHQUFHLGlEQUFpRDtJQUV6RCwyQ0FBMkM7SUFDM0MsTUFBTWlELHFCQUFxQmpFLGtEQUFXQSxDQUFDO1FBQ3JDLElBQUksQ0FBQ1csZ0JBQWdCTSxpQkFBaUIsT0FBTztRQUU3Q0MsbUJBQW1CO1FBQ25CLElBQUk7WUFDRixNQUFNZ0QsV0FBVyxNQUFNckIsTUFBTSxxQkFBcUI7Z0JBQ2hEc0IsUUFBUTtnQkFDUnJCLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQXNCLE1BQU1WLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJoRDtvQkFDQTBELFVBQVU7b0JBQ1ZDLFdBQVdDLFVBQVVELFNBQVM7Z0JBQ2hDO1lBQ0Y7WUFFQSxJQUFJLENBQUNKLFNBQVNsQixFQUFFLEVBQUU7Z0JBQ2hCbkIsUUFBUW9CLEtBQUssQ0FBQyx5QkFBeUJpQixTQUFTaEIsTUFBTTtnQkFDdEQsT0FBTztZQUNUO1lBRUEsTUFBTXNCLE9BQU8sTUFBTU4sU0FBU2IsSUFBSTtZQUNoQzNDLFNBQVM4RCxLQUFLQyxXQUFXO1lBQ3pCN0QsZ0JBQWdCNEQsS0FBSzdELFlBQVk7WUFDakMrQixhQUFhZSxPQUFPLENBQUMsY0FBY2UsS0FBS0MsV0FBVztZQUNuRC9CLGFBQWFlLE9BQU8sQ0FBQyxzQkFBc0JlLEtBQUs3RCxZQUFZO1lBRTVEa0IsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNULEVBQUUsT0FBT21CLE9BQU87WUFDZHBCLFFBQVFvQixLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO1FBQ1QsU0FBVTtZQUNSL0IsbUJBQW1CO1FBQ3JCO0lBQ0YsR0FBRztRQUFDUDtRQUFjTTtLQUFnQjtJQUVsQywyQ0FBMkM7SUFDM0MsTUFBTXlELG9CQUFvQjFFLGtEQUFXQSxDQUFDO1FBQ3BDLElBQUksQ0FBQ1MsT0FBTyxPQUFPO1FBRW5CLElBQUk7WUFDRixNQUFNeUQsV0FBVyxNQUFNckIsTUFBTSw2QkFBNkI7Z0JBQ3hEQyxTQUFTO29CQUFFQyxlQUFlLENBQUMsT0FBTyxFQUFFdEMsTUFBTSxDQUFDO2dCQUFDO1lBQzlDO1lBRUEsSUFBSSxDQUFDeUQsU0FBU2xCLEVBQUUsRUFBRTtnQkFDaEJuQixRQUFRb0IsS0FBSyxDQUFDLGtDQUFrQ2lCLFNBQVNoQixNQUFNO2dCQUMvRCxPQUFPO1lBQ1Q7WUFFQSxPQUFPLE1BQU1nQixTQUFTYixJQUFJO1FBQzVCLEVBQUUsT0FBT0osT0FBTztZQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE9BQU87UUFDVDtJQUNGLEdBQUc7UUFBQ3hDO0tBQU07SUFFVixzREFBc0Q7SUFDdERWLGdEQUFTQSxDQUFDO1FBQ1I4QixRQUFRQyxHQUFHLENBQUM7UUFDWixNQUFNNkMsYUFBYWpDLGFBQWFDLE9BQU8sQ0FBQztRQUN4QyxNQUFNRixjQUFjQyxhQUFhQyxPQUFPLENBQUM7UUFDekMsTUFBTWlDLHFCQUFxQmxDLGFBQWFDLE9BQU8sQ0FBQztRQUVoRGQsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQztZQUM1QytDLGVBQWUsQ0FBQyxDQUFDRjtZQUNqQkcsZ0JBQWdCLENBQUMsQ0FBQ3JDO1lBQ2xCc0MsdUJBQXVCLENBQUMsQ0FBQ0g7UUFDM0I7UUFFQSxJQUFJRCxjQUFjbEMsYUFBYTtZQUM3QixNQUFNdUMsVUFBVXRCLEtBQUt1QixLQUFLLENBQUNOO1lBQzNCOUMsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQztnQkFDOUNvRCxVQUFVRixRQUFRakQsSUFBSTtnQkFDdEJvRCxXQUFXSCxRQUFRSSxLQUFLO2dCQUN4QkMsbUJBQW1CTCxRQUFRL0MsbUJBQW1CLElBQUkrQyxRQUFROUMsYUFBYTtnQkFDdkVvRCxtQkFBbUJOLFFBQVF4QixhQUFhO1lBQzFDO1lBRUEsc0VBQXNFO1lBQ3RFLE1BQU0rQixxQkFBcUI7Z0JBQ3pCLElBQUk7b0JBQ0YxRCxRQUFRQyxHQUFHLENBQUMsMkNBQTJDa0QsUUFBUWpELElBQUk7b0JBQ25FLE1BQU1tQyxXQUFXLE1BQU1yQixNQUFNLENBQUMsV0FBVyxFQUFFbUMsUUFBUWpELElBQUksQ0FBQyxDQUFDLEVBQUU7d0JBQ3pEZSxTQUFTOzRCQUFFQyxlQUFlLENBQUMsT0FBTyxFQUFFTixZQUFZLENBQUM7d0JBQUM7b0JBQ3BEO29CQUVBLElBQUl5QixTQUFTaEIsTUFBTSxLQUFLLEtBQUs7d0JBQzNCckIsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLHVFQUF1RTt3QkFDdkVZLGFBQWE4QyxVQUFVLENBQUM7d0JBQ3hCOUMsYUFBYThDLFVBQVUsQ0FBQzt3QkFDeEI5QyxhQUFhOEMsVUFBVSxDQUFDO3dCQUN4QnJFLE9BQU9zRSxPQUFPLENBQUM7d0JBQ2Y7b0JBQ0Y7b0JBRUEsSUFBSSxDQUFDdkIsU0FBU2xCLEVBQUUsRUFBRTt3QkFDaEJuQixRQUFRQyxHQUFHLENBQUMsaUNBQWlDb0MsU0FBU2hCLE1BQU0sRUFBRWdCLFNBQVNmLFVBQVU7d0JBQ2pGLDBFQUEwRTt3QkFDMUVULGFBQWE4QyxVQUFVLENBQUM7d0JBQ3hCOUMsYUFBYThDLFVBQVUsQ0FBQzt3QkFDeEI5QyxhQUFhOEMsVUFBVSxDQUFDO3dCQUN4QnJFLE9BQU9zRSxPQUFPLENBQUM7d0JBQ2Y7b0JBQ0Y7b0JBRUEsd0NBQXdDO29CQUN4QzVELFFBQVFDLEdBQUcsQ0FBQztvQkFDWnRCLFFBQVF3RTtvQkFDUnRFLFNBQVMrQjtvQkFDVCxJQUFJbUMsb0JBQW9CO3dCQUN0QmhFLGdCQUFnQmdFO29CQUNsQjtvQkFFQSw4RUFBOEU7b0JBQzlFLDZGQUE2RjtvQkFDN0YvQyxRQUFRQyxHQUFHLENBQUM7b0JBQ1o0RCxXQUFXO3dCQUNUbEUsNkJBQTZCd0QsU0FBU3ZDO29CQUN4QyxHQUFHO2dCQUNMLEVBQUUsT0FBT1EsT0FBTztvQkFDZHBCLFFBQVFvQixLQUFLLENBQUMsK0JBQStCQTtvQkFDN0MsdUVBQXVFO29CQUN2RVAsYUFBYThDLFVBQVUsQ0FBQztvQkFDeEI5QyxhQUFhOEMsVUFBVSxDQUFDO29CQUN4QjlDLGFBQWE4QyxVQUFVLENBQUM7b0JBQ3hCckUsT0FBT3NFLE9BQU8sQ0FBQztnQkFDakI7WUFDRjtZQUVBLDRDQUE0QztZQUM1Q0Y7UUFDRixPQUFPO1lBQ0wxRCxRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLDhFQUE4RTtRQUM5RSxJQUFJLENBQUNXLGVBQWUsQ0FBQ2tELE9BQU9DLFFBQVEsQ0FBQ0MsUUFBUSxDQUFDQyxRQUFRLENBQUMsVUFBVTtZQUMvRGpFLFFBQVFDLEdBQUcsQ0FBQztZQUNaWCxPQUFPc0UsT0FBTyxDQUFDO1FBQ2pCO1FBRUEzRSxXQUFXO0lBQ2IsR0FBRyxFQUFFLEdBQUcsaUZBQWlGO0lBRXpGLDREQUE0RDtJQUM1RGYsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNVLFNBQVMsQ0FBQ0UsY0FBYztZQUMzQmtCLFFBQVFDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxNQUFNaUUsV0FBV0MsWUFBWTtZQUMzQixJQUFJO2dCQUNGbkUsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLGdDQUFnQztnQkFDaEMsTUFBTW1FLE1BQU0sTUFBTXBELE1BQU0sc0JBQXNCO29CQUM1Q0MsU0FBUzt3QkFBRUMsZUFBZSxDQUFDLE9BQU8sRUFBRXRDLE1BQU0sQ0FBQztvQkFBQztnQkFDOUM7Z0JBRUEsSUFBSSxDQUFDd0YsSUFBSWpELEVBQUUsRUFBRTtvQkFDWG5CLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixtQ0FBbUM7b0JBQ25DLE1BQU1vRSxpQkFBaUIsTUFBTWpDO29CQUM3QixJQUFJLENBQUNpQyxnQkFBZ0I7d0JBQ25CckUsUUFBUUMsR0FBRyxDQUFDO3dCQUNaWSxhQUFhOEMsVUFBVSxDQUFDO3dCQUN4QjlDLGFBQWE4QyxVQUFVLENBQUM7d0JBQ3hCOUMsYUFBYThDLFVBQVUsQ0FBQzt3QkFDeEJyRSxPQUFPc0UsT0FBTyxDQUFDO29CQUNqQixPQUFPO3dCQUNMNUQsUUFBUUMsR0FBRyxDQUFDO29CQUNkO2dCQUNGLE9BQU87b0JBQ0xELFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLEVBQUUsT0FBT21CLE9BQU87Z0JBQ2RwQixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osc0NBQXNDO2dCQUN0QyxNQUFNb0UsaUJBQWlCLE1BQU1qQztnQkFDN0IsSUFBSSxDQUFDaUMsZ0JBQWdCO29CQUNuQnJFLFFBQVFDLEdBQUcsQ0FBQztvQkFDWlksYUFBYThDLFVBQVUsQ0FBQztvQkFDeEI5QyxhQUFhOEMsVUFBVSxDQUFDO29CQUN4QjlDLGFBQWE4QyxVQUFVLENBQUM7b0JBQ3hCckUsT0FBT3NFLE9BQU8sQ0FBQztnQkFDakIsT0FBTztvQkFDTDVELFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGO1FBQ0YsR0FBRyxLQUFLLEtBQUssT0FBTyxhQUFhO1FBRWpDLE9BQU8sSUFBTXFFLGNBQWNKO0lBQzdCLEdBQUc7UUFBQ3RGO1FBQU9FO1FBQWNzRDtRQUFvQjlDO0tBQU87SUFFcEQsTUFBTWlGLFFBQVEsQ0FBQzdGLE1BQVlFLE9BQWU0RjtRQUN4Q3hFLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7WUFDNUNvRCxVQUFVM0UsS0FBS3dCLElBQUk7WUFDbkJvRCxXQUFXNUUsS0FBSzZFLEtBQUs7WUFDckJDLG1CQUFtQjlFLEtBQUswQixtQkFBbUIsSUFBSTFCLEtBQUsyQixhQUFhO1lBQ2pFb0UseUJBQXlCL0YsS0FBSzBCLG1CQUFtQjtZQUNqRHNFLHlCQUF5QmhHLEtBQUsyQixhQUFhO1lBQzNDb0QsbUJBQW1CL0UsS0FBS2lELGFBQWE7WUFDckNnRCxVQUFVLENBQUMsQ0FBQy9GO1lBQ1pnRyxpQkFBaUIsQ0FBQyxDQUFDSjtRQUNyQjtRQUVBN0YsUUFBUUQ7UUFDUkcsU0FBU0Q7UUFDVEcsZ0JBQWdCeUY7UUFDaEIzRCxhQUFhZSxPQUFPLENBQUMsYUFBYUMsS0FBS0MsU0FBUyxDQUFDcEQ7UUFDakRtQyxhQUFhZSxPQUFPLENBQUMsY0FBY2hEO1FBQ25DaUMsYUFBYWUsT0FBTyxDQUFDLHNCQUFzQjRDO1FBRTNDLDhFQUE4RTtRQUM5RSw2RkFBNkY7UUFDN0Z4RSxRQUFRQyxHQUFHLENBQUM7UUFDWjRELFdBQVc7WUFDVGxFLDZCQUE2QmpCLE1BQU1FO1FBQ3JDLEdBQUc7SUFDTDtJQUVBLE1BQU1pRyxTQUFTO1FBQ2I3RSxRQUFRQyxHQUFHLENBQUM7UUFFWiwyQ0FBMkM7UUFDM0MsSUFBSXJCLFNBQVNFLGNBQWM7WUFDekIsSUFBSTtnQkFDRixNQUFNa0MsTUFBTSxvQkFBb0I7b0JBQzlCc0IsUUFBUTtvQkFDUnJCLFNBQVM7d0JBQ1AsZ0JBQWdCO3dCQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVyQyxNQUFNLENBQUM7b0JBQ3BDO29CQUNBMkQsTUFBTVYsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQmhEO3dCQUNBZ0csV0FBVztvQkFDYjtnQkFDRjtZQUNGLEVBQUUsT0FBTzFELE9BQU87Z0JBQ2RwQixRQUFRb0IsS0FBSyxDQUFDLHdCQUF3QkE7WUFDdEMsa0RBQWtEO1lBQ3BEO1FBQ0Y7UUFFQXpDLFFBQVE7UUFDUkUsU0FBUztRQUNURSxnQkFBZ0I7UUFDaEI4QixhQUFhOEMsVUFBVSxDQUFDO1FBQ3hCOUMsYUFBYThDLFVBQVUsQ0FBQztRQUN4QjlDLGFBQWE4QyxVQUFVLENBQUM7UUFFeEIzRCxRQUFRQyxHQUFHLENBQUM7UUFDWlgsT0FBT3NFLE9BQU8sQ0FBQztJQUNqQjtJQUVBLE1BQU1tQixrQkFBa0I1RyxrREFBV0EsQ0FBQyxPQUFPK0I7UUFDekMsSUFBSSxDQUFDdEIsT0FBTyxPQUFPO1FBRW5CLHFDQUFxQztRQUNyQyxJQUFJLENBQUNzQixRQUFRLE9BQU9BLFNBQVMsWUFBWUEsS0FBS1MsSUFBSSxPQUFPLElBQUk7WUFDM0RYLFFBQVFDLEdBQUcsQ0FBQywyQ0FBMkNDO1lBQ3ZELE9BQU87UUFDVDtRQUVBLElBQUk7WUFDRixNQUFNbUMsV0FBVyxNQUFNckIsTUFBTSxDQUFDLFdBQVcsRUFBRWQsS0FBSyxDQUFDLEVBQUU7Z0JBQ2pEZSxTQUFTO29CQUFFQyxlQUFlLENBQUMsT0FBTyxFQUFFdEMsTUFBTSxDQUFDO2dCQUFDO1lBQzlDO1lBRUEsSUFBSXlELFNBQVNoQixNQUFNLEtBQUssS0FBSyxPQUFPO1lBQ3BDLElBQUksQ0FBQ2dCLFNBQVNsQixFQUFFLEVBQUUsTUFBTSxJQUFJNkQsTUFBTTtZQUVsQyxPQUFPO1FBQ1QsRUFBRSxPQUFPNUQsT0FBTztZQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE9BQU87UUFDVDtJQUNGLEdBQUc7UUFBQ3hDO0tBQU07SUFFVixNQUFNcUcsa0JBQWtCOUcsa0RBQVdBLENBQUMsT0FBT2tDLGVBQXVCc0I7UUFDaEUsSUFBSSxDQUFDakQsTUFBTXdCLFFBQVEsQ0FBQ3RCLE9BQU87WUFDekIsTUFBTSxJQUFJb0csTUFBTTtRQUNsQjtRQUVBLDBDQUEwQztRQUMxQyxJQUFJLE9BQU90RyxLQUFLd0IsSUFBSSxLQUFLLFlBQVl4QixLQUFLd0IsSUFBSSxDQUFDUyxJQUFJLE9BQU8sSUFBSTtZQUM1RCxNQUFNLElBQUlxRSxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtZQUNGLHFDQUFxQztZQUNyQyxNQUFNM0MsV0FBVyxNQUFNckIsTUFBTSxDQUFDLFdBQVcsRUFBRXRDLEtBQUt3QixJQUFJLENBQUMsVUFBVSxDQUFDLEVBQUU7Z0JBQ2hFb0MsUUFBUTtnQkFDUnJCLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQkMsZUFBZSxDQUFDLE9BQU8sRUFBRXRDLE1BQU0sQ0FBQztnQkFDbEM7Z0JBQ0EyRCxNQUFNVixLQUFLQyxTQUFTLENBQUM7b0JBQUV6QjtnQkFBYztZQUN2QztZQUVBLElBQUksQ0FBQ2dDLFNBQVNsQixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSTZELE1BQU07WUFDbEI7WUFFQSw4Q0FBOEM7WUFDOUMsTUFBTXRELGNBQW9CO2dCQUN4QixHQUFHaEQsSUFBSTtnQkFDUDJCLGVBQWVBO2dCQUNmc0IsZUFBZUE7WUFDakI7WUFFQSxzQkFBc0I7WUFDdEJkLGFBQWFlLE9BQU8sQ0FBQyxhQUFhQyxLQUFLQyxTQUFTLENBQUNKO1lBRWpELHVCQUF1QjtZQUN2Qi9DLFFBQVErQztZQUVSMUIsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQztnQkFBRUk7Z0JBQWVzQjtZQUFjO1FBQ2pGLEVBQUUsT0FBT1AsT0FBTztZQUNkcEIsUUFBUW9CLEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDLE1BQU1BO1FBQ1I7SUFDRixHQUFHO1FBQUMxQztRQUFNRTtLQUFNO0lBRWhCLHFCQUNFLDhEQUFDTixZQUFZNEcsUUFBUTtRQUNuQkMsT0FBTztZQUNMekc7WUFDQUU7WUFDQUU7WUFDQUU7WUFDQXVGO1lBQ0FNO1lBQ0F6QztZQUNBUztZQUNBbEQ7WUFDQW9GO1lBQ0FFO1FBQ0Y7a0JBRUN4Rzs7Ozs7O0FBR1A7QUFFTyxTQUFTMkc7SUFDZCxNQUFNQyxVQUFVckgsaURBQVVBLENBQUNNO0lBQzNCLElBQUksQ0FBQytHLFNBQVM7UUFDWixNQUFNLElBQUlMLE1BQU07SUFDbEI7SUFDQSxPQUFPSztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvLi9jb250ZXh0cy9BdXRoQ29udGV4dC50c3g/NmQ4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCBSZWFjdE5vZGUsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcblxyXG4vLyBFbmhhbmNlZCBVc2VyIGludGVyZmFjZSB0byBtYXRjaCBiYWNrZW5kIFVzZXJJbmZvRHRvXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XHJcbiAgdXVpZDogc3RyaW5nO1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgZmlyc3ROYW1lOiBzdHJpbmcgfCBudWxsO1xyXG4gIGxhc3ROYW1lOiBzdHJpbmcgfCBudWxsO1xyXG4gIHBob25lOiBzdHJpbmcgfCBudWxsO1xyXG4gIGlzQWN0aXZlOiBib29sZWFuO1xyXG4gIHJvbGVzOiBVc2VyUm9sZVtdO1xyXG4gIHdhcmVob3VzZVV1aWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgd2FyZWhvdXNlVXVpZFN0cmluZz86IHN0cmluZyB8IG51bGw7IC8vIEJhY2tlbmQgZmllbGQgbmFtZVxyXG4gIHZhblV1aWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XHJcbiAgLy8gTGVnYWN5IHN1cHBvcnQgZmllbGRzXHJcbiAgbmFtZT86IHN0cmluZztcclxuICBhdmF0YXJVcmw/OiBzdHJpbmc7XHJcbiAgdXNlclR5cGU/OiAnc3VwZXInIHwgJ3VzZXInO1xyXG4gIHdhcmVob3VzZU5hbWU/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlclJvbGUge1xyXG4gIHV1aWQ6IHN0cmluZztcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgcGVybWlzc2lvbnM6IHN0cmluZ1tdO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFNlc3Npb25TdGF0cyB7XHJcbiAgYWN0aXZlVG9rZW5zOiBudW1iZXI7XHJcbiAgdG90YWxUb2tlbnM6IG51bWJlcjtcclxuICBsYXN0VXNlZDogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTZWN1cml0eVN0YXR1cyB7XHJcbiAgaXNMb2NrZWQ6IGJvb2xlYW47XHJcbiAgbG9ja2VkVW50aWw6IHN0cmluZyB8IG51bGw7XHJcbiAgcmVtYWluaW5nQXR0ZW1wdHM6IG51bWJlcjtcclxuICBzdXNwaWNpb3VzQWN0aXZpdHlEZXRlY3RlZDogYm9vbGVhbjtcclxuICByZWNlbnRFdmVudHM6IFNlY3VyaXR5RXZlbnRbXTtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTZWN1cml0eUV2ZW50IHtcclxuICBldmVudFR5cGU6IHN0cmluZztcclxuICB0aW1lc3RhbXA6IHN0cmluZztcclxuICBpcEFkZHJlc3M/OiBzdHJpbmc7XHJcbiAgc3VjY2VzczogYm9vbGVhbjtcclxufVxyXG5cclxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XHJcbiAgdXNlcjogVXNlciB8IG51bGw7XHJcbiAgdG9rZW46IHN0cmluZyB8IG51bGw7XHJcbiAgcmVmcmVzaFRva2VuOiBzdHJpbmcgfCBudWxsO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgbG9naW46ICh1c2VyOiBVc2VyLCB0b2tlbjogc3RyaW5nLCByZWZyZXNoVG9rZW46IHN0cmluZykgPT4gdm9pZDtcclxuICBsb2dvdXQ6ICgpID0+IHZvaWQ7XHJcbiAgcmVmcmVzaEFjY2Vzc1Rva2VuOiAoKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xyXG4gIGdldFNlY3VyaXR5U3RhdHVzOiAoKSA9PiBQcm9taXNlPFNlY3VyaXR5U3RhdHVzIHwgbnVsbD47XHJcbiAgZmV0Y2hBbmRQZXJzaXN0V2FyZWhvdXNlSW5mbzogKHVzZXJPYmo/OiBVc2VyLCB0b2tlbj86IHN0cmluZyB8IG51bGwpID0+IFByb21pc2U8dm9pZD47XHJcbiAgY2hlY2tVc2VyRXhpc3RzOiAodXVpZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xyXG4gIHN3aXRjaFdhcmVob3VzZTogKHdhcmVob3VzZVV1aWQ6IHN0cmluZywgd2FyZWhvdXNlTmFtZTogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG59XHJcblxyXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0Tm9kZSB9KSB7XHJcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFt0b2tlbiwgc2V0VG9rZW5dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW3JlZnJlc2hUb2tlbiwgc2V0UmVmcmVzaFRva2VuXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtmZXRjaGluZ1dhcmVob3VzZSwgc2V0RmV0Y2hpbmdXYXJlaG91c2VdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtyZWZyZXNoaW5nVG9rZW4sIHNldFJlZnJlc2hpbmdUb2tlbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgXHJcbiAgLy8gVXNlIHJlZnMgdG8gYXZvaWQgZGVwZW5kZW5jeSBpc3N1ZXNcclxuICBjb25zdCBmZXRjaGluZ1dhcmVob3VzZVJlZiA9IHVzZVJlZihmYWxzZSk7XHJcbiAgY29uc3QgdG9rZW5SZWYgPSB1c2VSZWY8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgdXNlclJlZiA9IHVzZVJlZjxVc2VyIHwgbnVsbD4obnVsbCk7XHJcbiAgXHJcbiAgLy8gVXBkYXRlIHJlZnMgd2hlbiBzdGF0ZSBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoaW5nV2FyZWhvdXNlUmVmLmN1cnJlbnQgPSBmZXRjaGluZ1dhcmVob3VzZTtcclxuICB9LCBbZmV0Y2hpbmdXYXJlaG91c2VdKTtcclxuICBcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgdG9rZW5SZWYuY3VycmVudCA9IHRva2VuO1xyXG4gIH0sIFt0b2tlbl0pO1xyXG4gIFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICB1c2VyUmVmLmN1cnJlbnQgPSB1c2VyO1xyXG4gIH0sIFt1c2VyXSk7XHJcblxyXG4gIC8vIEhlbHBlciB0byBmZXRjaCB3YXJlaG91c2UgaW5mbyBpbiBiYWNrZ3JvdW5kXHJcbiAgY29uc3QgZmV0Y2hBbmRQZXJzaXN0V2FyZWhvdXNlSW5mbyA9IHVzZUNhbGxiYWNrKGFzeW5jICh1c2VyT2JqUGFyYW0/OiBVc2VyLCB0b2tlblBhcmFtPzogc3RyaW5nIHwgbnVsbCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW5Ub1VzZSA9IHRva2VuUGFyYW0gIT09IHVuZGVmaW5lZCA/IHRva2VuUGFyYW0gOiB0b2tlblJlZi5jdXJyZW50O1xyXG4gICAgY29uc3QgdXNlclRvRmV0Y2ggPSB1c2VyT2JqUGFyYW0gfHwgdXNlclJlZi5jdXJyZW50O1xyXG4gICAgXHJcbiAgICBjb25zb2xlLmxvZygnZmV0Y2hBbmRQZXJzaXN0V2FyZWhvdXNlSW5mbyBjYWxsZWQgd2l0aDonLCB7XHJcbiAgICAgIHRva2VuVG9Vc2U6ICEhdG9rZW5Ub1VzZSxcclxuICAgICAgdXNlclRvRmV0Y2g6IHVzZXJUb0ZldGNoPy51dWlkLFxyXG4gICAgICB1c2VyVG9GZXRjaFdhcmVob3VzZVV1aWQ6IHVzZXJUb0ZldGNoPy53YXJlaG91c2VVdWlkU3RyaW5nIHx8IHVzZXJUb0ZldGNoPy53YXJlaG91c2VVdWlkLFxyXG4gICAgICBmZXRjaGluZ1dhcmVob3VzZTogZmV0Y2hpbmdXYXJlaG91c2VSZWYuY3VycmVudCxcclxuICAgICAgY3VycmVudFVzZXI6IHVzZXJSZWYuY3VycmVudD8udXVpZCxcclxuICAgICAgY3VycmVudFVzZXJXYXJlaG91c2VVdWlkOiB1c2VyUmVmLmN1cnJlbnQ/LndhcmVob3VzZVV1aWRTdHJpbmcgfHwgdXNlclJlZi5jdXJyZW50Py53YXJlaG91c2VVdWlkXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgaWYgKCF0b2tlblRvVXNlIHx8ICF1c2VyVG9GZXRjaD8udXVpZCB8fCBmZXRjaGluZ1dhcmVob3VzZVJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEFuZFBlcnNpc3RXYXJlaG91c2VJbmZvIGVhcmx5IHJldHVybjonLCB7XHJcbiAgICAgICAgbm9Ub2tlbjogIXRva2VuVG9Vc2UsXHJcbiAgICAgICAgbm9Vc2VyVXVpZDogIXVzZXJUb0ZldGNoPy51dWlkLFxyXG4gICAgICAgIGFscmVhZHlGZXRjaGluZzogZmV0Y2hpbmdXYXJlaG91c2VSZWYuY3VycmVudFxyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBBZGRpdGlvbmFsIHNhZmV0eSBjaGVjazogZW5zdXJlIFVVSUQgaXMgYSB2YWxpZCBzdHJpbmdcclxuICAgIGlmICh0eXBlb2YgdXNlclRvRmV0Y2gudXVpZCAhPT0gJ3N0cmluZycgfHwgdXNlclRvRmV0Y2gudXVpZC50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEFuZFBlcnNpc3RXYXJlaG91c2VJbmZvIGVhcmx5IHJldHVybjogaW52YWxpZCBVVUlEOicsIHVzZXJUb0ZldGNoLnV1aWQpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIEFkZGl0aW9uYWwgY2hlY2s6IGlmIHdlIGRvbid0IGhhdmUgYSB2YWxpZCB0b2tlbiBpbiBsb2NhbFN0b3JhZ2UsIGRvbid0IHByb2NlZWRcclxuICAgIGNvbnN0IHN0b3JlZFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2RpZG9fdG9rZW4nKTtcclxuICAgIGlmICghc3RvcmVkVG9rZW4pIHtcclxuICAgICAgY29uc29sZS5sb2coJ2ZldGNoQW5kUGVyc2lzdFdhcmVob3VzZUluZm8gZWFybHkgcmV0dXJuOiBubyBzdG9yZWQgdG9rZW4nKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBzZXRGZXRjaGluZ1dhcmVob3VzZSh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBsYXRlc3QgdXNlciBpbmZvIGZvciBVVUlEOicsIHVzZXJUb0ZldGNoLnV1aWQpO1xyXG4gICAgICAvLyBBbHdheXMgZmV0Y2ggdGhlIGxhdGVzdCB1c2VyIGluZm8gZnJvbSBiYWNrZW5kIHRvIGVuc3VyZSB3ZSBoYXZlIGN1cnJlbnQgZGF0YVxyXG4gICAgICBjb25zdCB1c2VyUmVzID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlcnMvJHt1c2VyVG9GZXRjaC51dWlkfWAsIHtcclxuICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlblRvVXNlfWAgfSxcclxuICAgICAgfSk7XHJcbiAgICAgIGlmICghdXNlclJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCB1c2VyIGluZm86JywgdXNlclJlcy5zdGF0dXMsIHVzZXJSZXMuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICAgIGNvbnN0IGxhdGVzdFVzZXIgPSBhd2FpdCB1c2VyUmVzLmpzb24oKTtcclxuICAgICAgY29uc29sZS5sb2coJ0xhdGVzdCB1c2VyIGluZm8gZnJvbSBBUEk6JywgbGF0ZXN0VXNlcik7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdXYXJlaG91c2UgVVVJRCBmcm9tIGJhY2tlbmQ6Jywge1xyXG4gICAgICAgIHdhcmVob3VzZVV1aWRTdHJpbmc6IGxhdGVzdFVzZXIud2FyZWhvdXNlVXVpZFN0cmluZyxcclxuICAgICAgICB3YXJlaG91c2VVdWlkOiBsYXRlc3RVc2VyLndhcmVob3VzZVV1aWQsXHJcbiAgICAgICAgZmluYWxXYXJlaG91c2VVdWlkOiBsYXRlc3RVc2VyLndhcmVob3VzZVV1aWRTdHJpbmcgfHwgbGF0ZXN0VXNlci53YXJlaG91c2VVdWlkXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgYSB3YXJlaG91c2UgYXNzaWduZWRcclxuICAgICAgY29uc3Qgd2FyZWhvdXNlVXVpZCA9IGxhdGVzdFVzZXIud2FyZWhvdXNlVXVpZFN0cmluZyB8fCBsYXRlc3RVc2VyLndhcmVob3VzZVV1aWQ7XHJcbiAgICAgIGlmICghd2FyZWhvdXNlVXVpZCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdObyB3YXJlaG91c2UgYXNzaWduZWQgdG8gdXNlciwgc2V0dGluZyB1c2VyIHdpdGhvdXQgd2FyZWhvdXNlIGluZm8nKTtcclxuICAgICAgICBjb25zdCB1cGRhdGVkVXNlcjogVXNlciA9IHtcclxuICAgICAgICAgIC4uLmxhdGVzdFVzZXIsXHJcbiAgICAgICAgICB3YXJlaG91c2VVdWlkOiBudWxsLFxyXG4gICAgICAgICAgd2FyZWhvdXNlTmFtZTogbnVsbCxcclxuICAgICAgICB9O1xyXG4gICAgICAgIHNldFVzZXIodXBkYXRlZFVzZXIpO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZGlkb191c2VyXCIsIEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRVc2VyKSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnRmV0Y2hpbmcgd2FyZWhvdXNlIGluZm8gZm9yIFVVSUQ6Jywgd2FyZWhvdXNlVXVpZCk7XHJcbiAgICAgIGNvbnN0IHdhcmVob3VzZVJlcyA9IGF3YWl0IGZldGNoKGAvYXBpL3dhcmVob3VzZXMvJHt3YXJlaG91c2VVdWlkfWAsIHtcclxuICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlblRvVXNlfWAgfSxcclxuICAgICAgfSk7XHJcbiAgICAgIFxyXG4gICAgICBpZiAoIXdhcmVob3VzZVJlcy5vaykge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCB3YXJlaG91c2UgaW5mbzonLCB3YXJlaG91c2VSZXMuc3RhdHVzLCB3YXJlaG91c2VSZXMuc3RhdHVzVGV4dCk7XHJcbiAgICAgICAgLy8gSWYgd2FyZWhvdXNlIGZldGNoIGZhaWxzLCBzdGlsbCB1cGRhdGUgdXNlciB3aXRoIGN1cnJlbnQgZGF0YSBidXQgd2l0aG91dCB3YXJlaG91c2UgbmFtZVxyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRVc2VyOiBVc2VyID0ge1xyXG4gICAgICAgICAgLi4ubGF0ZXN0VXNlcixcclxuICAgICAgICAgIHdhcmVob3VzZVV1aWQ6IHdhcmVob3VzZVV1aWQsXHJcbiAgICAgICAgICB3YXJlaG91c2VOYW1lOiBudWxsLCAvLyBXYXJlaG91c2UgbmFtZSB1bmtub3duXHJcbiAgICAgICAgfTtcclxuICAgICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImRpZG9fdXNlclwiLCBKU09OLnN0cmluZ2lmeSh1cGRhdGVkVXNlcikpO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgY29uc3Qgd2FyZWhvdXNlID0gYXdhaXQgd2FyZWhvdXNlUmVzLmpzb24oKTtcclxuICAgICAgY29uc29sZS5sb2coJ1dhcmVob3VzZSBpbmZvIGZyb20gQVBJOicsIHdhcmVob3VzZSk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCB1cGRhdGVkVXNlcjogVXNlciA9IHtcclxuICAgICAgICAuLi5sYXRlc3RVc2VyLFxyXG4gICAgICAgIHdhcmVob3VzZVV1aWQ6IHdhcmVob3VzZVV1aWQsXHJcbiAgICAgICAgd2FyZWhvdXNlTmFtZTogd2FyZWhvdXNlLm5hbWUsXHJcbiAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICBjb25zb2xlLmxvZygnU2V0dGluZyB1cGRhdGVkIHVzZXIgd2l0aCB3YXJlaG91c2UgaW5mbzonLCB1cGRhdGVkVXNlcik7XHJcbiAgICAgIHNldFVzZXIodXBkYXRlZFVzZXIpO1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImRpZG9fdXNlclwiLCBKU09OLnN0cmluZ2lmeSh1cGRhdGVkVXNlcikpO1xyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHdhcmVob3VzZSBpbmZvOicsIGVycik7XHJcbiAgICAgIC8vIE9uIGVycm9yLCBzdGlsbCB0cnkgdG8gdXBkYXRlIHVzZXIgd2l0aCBjdXJyZW50IGRhdGEgZnJvbSBiYWNrZW5kXHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgdXNlclJlcyA9IGF3YWl0IGZldGNoKGAvYXBpL3VzZXJzLyR7dXNlclRvRmV0Y2gudXVpZH1gLCB7XHJcbiAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlblRvVXNlfWAgfSxcclxuICAgICAgICB9KTtcclxuICAgICAgICBpZiAodXNlclJlcy5vaykge1xyXG4gICAgICAgICAgY29uc3QgbGF0ZXN0VXNlciA9IGF3YWl0IHVzZXJSZXMuanNvbigpO1xyXG4gICAgICAgICAgY29uc3QgdXBkYXRlZFVzZXI6IFVzZXIgPSB7XHJcbiAgICAgICAgICAgIC4uLmxhdGVzdFVzZXIsXHJcbiAgICAgICAgICAgIHdhcmVob3VzZVV1aWQ6IGxhdGVzdFVzZXIud2FyZWhvdXNlVXVpZFN0cmluZyB8fCBsYXRlc3RVc2VyLndhcmVob3VzZVV1aWQsXHJcbiAgICAgICAgICAgIHdhcmVob3VzZU5hbWU6IG51bGwsIC8vIENvdWxkIG5vdCBmZXRjaCB3YXJlaG91c2UgbmFtZSBkdWUgdG8gZXJyb3JcclxuICAgICAgICAgIH07XHJcbiAgICAgICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZGlkb191c2VyXCIsIEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRVc2VyKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChmYWxsYmFja0Vycikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGZhbGxiYWNrIHVzZXIgdXBkYXRlOicsIGZhbGxiYWNrRXJyKTtcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0RmV0Y2hpbmdXYXJlaG91c2UoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTsgLy8gUmVtb3ZlIGFsbCBkZXBlbmRlbmNpZXMgc2luY2Ugd2UncmUgdXNpbmcgcmVmc1xyXG5cclxuICAvLyBSZWZyZXNoIGFjY2VzcyB0b2tlbiB1c2luZyByZWZyZXNoIHRva2VuXHJcbiAgY29uc3QgcmVmcmVzaEFjY2Vzc1Rva2VuID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xyXG4gICAgaWYgKCFyZWZyZXNoVG9rZW4gfHwgcmVmcmVzaGluZ1Rva2VuKSByZXR1cm4gZmFsc2U7XHJcbiAgICBcclxuICAgIHNldFJlZnJlc2hpbmdUb2tlbih0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9yZWZyZXNoJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgfSxcclxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICByZWZyZXNoVG9rZW4sXHJcbiAgICAgICAgICBjbGllbnRJcDogJ2Zyb250ZW5kJywgLy8gV2lsbCBiZSBvdmVycmlkZGVuIGJ5IGJhY2tlbmRcclxuICAgICAgICAgIHVzZXJBZ2VudDogbmF2aWdhdG9yLnVzZXJBZ2VudCxcclxuICAgICAgICB9KSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gcmVmcmVzaCBmYWlsZWQ6JywgcmVzcG9uc2Uuc3RhdHVzKTtcclxuICAgICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICAgIHNldFRva2VuKGRhdGEuYWNjZXNzVG9rZW4pO1xyXG4gICAgICBzZXRSZWZyZXNoVG9rZW4oZGF0YS5yZWZyZXNoVG9rZW4pO1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImRpZG9fdG9rZW5cIiwgZGF0YS5hY2Nlc3NUb2tlbik7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZGlkb19yZWZyZXNoX3Rva2VuXCIsIGRhdGEucmVmcmVzaFRva2VuKTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiByZWZyZXNoZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVmcmVzaGluZyB0b2tlbjonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFJlZnJlc2hpbmdUb2tlbihmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW3JlZnJlc2hUb2tlbiwgcmVmcmVzaGluZ1Rva2VuXSk7XHJcblxyXG4gIC8vIEdldCBzZWN1cml0eSBzdGF0dXMgZm9yIHRoZSBjdXJyZW50IHVzZXJcclxuICBjb25zdCBnZXRTZWN1cml0eVN0YXR1cyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpOiBQcm9taXNlPFNlY3VyaXR5U3RhdHVzIHwgbnVsbD4gPT4ge1xyXG4gICAgaWYgKCF0b2tlbikgcmV0dXJuIG51bGw7XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9zZWN1cml0eS1zdGF0dXMnLCB7XHJcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IHNlY3VyaXR5IHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMpO1xyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBzZWN1cml0eSBzdGF0dXM6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9LCBbdG9rZW5dKTtcclxuXHJcbiAgLy8gT24gaW5pdGlhbCBtb3VudDogcmVzdG9yZSBzZXNzaW9uIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dCBpbml0aWFsIG1vdW50IC0gY2hlY2tpbmcgbG9jYWxTdG9yYWdlJyk7XHJcbiAgICBjb25zdCBzdG9yZWRVc2VyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJkaWRvX3VzZXJcIik7XHJcbiAgICBjb25zdCBzdG9yZWRUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiZGlkb190b2tlblwiKTtcclxuICAgIGNvbnN0IHN0b3JlZFJlZnJlc2hUb2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiZGlkb19yZWZyZXNoX3Rva2VuXCIpO1xyXG4gICAgXHJcbiAgICBjb25zb2xlLmxvZygnU3RvcmVkIGRhdGEgZnJvbSBsb2NhbFN0b3JhZ2U6Jywge1xyXG4gICAgICBoYXNTdG9yZWRVc2VyOiAhIXN0b3JlZFVzZXIsXHJcbiAgICAgIGhhc1N0b3JlZFRva2VuOiAhIXN0b3JlZFRva2VuLFxyXG4gICAgICBoYXNTdG9yZWRSZWZyZXNoVG9rZW46ICEhc3RvcmVkUmVmcmVzaFRva2VuXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgaWYgKHN0b3JlZFVzZXIgJiYgc3RvcmVkVG9rZW4pIHtcclxuICAgICAgY29uc3QgdXNlck9iaiA9IEpTT04ucGFyc2Uoc3RvcmVkVXNlcik7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdSZXN0b3JlZCB1c2VyIGZyb20gbG9jYWxTdG9yYWdlOicsIHtcclxuICAgICAgICB1c2VyVXVpZDogdXNlck9iai51dWlkLFxyXG4gICAgICAgIHVzZXJFbWFpbDogdXNlck9iai5lbWFpbCxcclxuICAgICAgICB1c2VyV2FyZWhvdXNlVXVpZDogdXNlck9iai53YXJlaG91c2VVdWlkU3RyaW5nIHx8IHVzZXJPYmoud2FyZWhvdXNlVXVpZCxcclxuICAgICAgICB1c2VyV2FyZWhvdXNlTmFtZTogdXNlck9iai53YXJlaG91c2VOYW1lXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8gVmFsaWRhdGUgdGhhdCB0aGUgdXNlciBVVUlEIGlzIHN0aWxsIHZhbGlkIGJ5IGZldGNoaW5nIGZyb20gYmFja2VuZFxyXG4gICAgICBjb25zdCB2YWxpZGF0ZVVzZXJFeGlzdHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdWYWxpZGF0aW5nIHVzZXIgVVVJRCBmcm9tIGxvY2FsU3RvcmFnZTonLCB1c2VyT2JqLnV1aWQpO1xyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS91c2Vycy8ke3VzZXJPYmoudXVpZH1gLCB7XHJcbiAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3N0b3JlZFRva2VufWAgfSxcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDQpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgVVVJRCBub3QgZm91bmQgaW4gYmFja2VuZCAoNDA0KSwgY2xlYXJpbmcgbG9jYWxTdG9yYWdlIGFuZCByZWRpcmVjdGluZyB0byBsb2dpbicpO1xyXG4gICAgICAgICAgICAvLyBVc2VyIHdhcyBkZWxldGVkL3JlY3JlYXRlZCwgY2xlYXIgbG9jYWxTdG9yYWdlIGFuZCByZWRpcmVjdCB0byBsb2dpblxyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb190b2tlbicpO1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb19yZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdkaWRvX3VzZXInKTtcclxuICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoJy9hdXRoJyk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnRmFpbGVkIHRvIHZhbGlkYXRlIHVzZXIgVVVJRDonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xyXG4gICAgICAgICAgICAvLyBJZiB3ZSBjYW4ndCB2YWxpZGF0ZSB0aGUgdXNlciwgY2xlYXIgbG9jYWxTdG9yYWdlIGFuZCByZWRpcmVjdCB0byBsb2dpblxyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb190b2tlbicpO1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb19yZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdkaWRvX3VzZXInKTtcclxuICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoJy9hdXRoJyk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gVXNlciBleGlzdHMsIHByb2NlZWQgd2l0aCBub3JtYWwgZmxvd1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgVVVJRCB2YWxpZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XHJcbiAgICAgICAgICBzZXRVc2VyKHVzZXJPYmopO1xyXG4gICAgICAgICAgc2V0VG9rZW4oc3RvcmVkVG9rZW4pO1xyXG4gICAgICAgICAgaWYgKHN0b3JlZFJlZnJlc2hUb2tlbikge1xyXG4gICAgICAgICAgICBzZXRSZWZyZXNoVG9rZW4oc3RvcmVkUmVmcmVzaFRva2VuKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gQWx3YXlzIHRyaWdnZXIgYmFja2dyb3VuZCBmZXRjaCB0byBlbnN1cmUgd2UgaGF2ZSB0aGUgbGF0ZXN0IHdhcmVob3VzZSBpbmZvXHJcbiAgICAgICAgICAvLyBUaGlzIGlzIGVzcGVjaWFsbHkgaW1wb3J0YW50IGFmdGVyIGRhdGFiYXNlIGNsZWFyaW5nIHdoZW4gd2FyZWhvdXNlIFVVSURzIG1heSBoYXZlIGNoYW5nZWRcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUcmlnZ2VyaW5nIGJhY2tncm91bmQgd2FyZWhvdXNlIGluZm8gZmV0Y2ggdG8gZW5zdXJlIGxhdGVzdCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlJyk7XHJcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgZmV0Y2hBbmRQZXJzaXN0V2FyZWhvdXNlSW5mbyh1c2VyT2JqLCBzdG9yZWRUb2tlbik7XHJcbiAgICAgICAgICB9LCAwKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdmFsaWRhdGluZyB1c2VyIFVVSUQ6JywgZXJyb3IpO1xyXG4gICAgICAgICAgLy8gSWYgdGhlcmUncyBhIG5ldHdvcmsgZXJyb3IsIGNsZWFyIGxvY2FsU3RvcmFnZSBhbmQgcmVkaXJlY3QgdG8gbG9naW5cclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdkaWRvX3Rva2VuJyk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb19yZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb191c2VyJyk7XHJcbiAgICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2F1dGgnKTtcclxuICAgICAgICB9XHJcbiAgICAgIH07XHJcbiAgICAgIFxyXG4gICAgICAvLyBWYWxpZGF0ZSB1c2VyIGV4aXN0cyBiZWZvcmUgc2V0dGluZyBzdGF0ZVxyXG4gICAgICB2YWxpZGF0ZVVzZXJFeGlzdHMoKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdObyBzdG9yZWQgdXNlci90b2tlbiBmb3VuZCBpbiBsb2NhbFN0b3JhZ2UnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gSWYgd2UgaGF2ZSBubyB2YWxpZCBhdXRoZW50aWNhdGlvbiBhbmQgd2UncmUgbm90IG9uIHRoZSBhdXRoIHBhZ2UsIHJlZGlyZWN0XHJcbiAgICBpZiAoIXN0b3JlZFRva2VuICYmICF3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUuaW5jbHVkZXMoJy9hdXRoJykpIHtcclxuICAgICAgY29uc29sZS5sb2coJ05vIHZhbGlkIGF1dGhlbnRpY2F0aW9uIGZvdW5kLCByZWRpcmVjdGluZyB0byBsb2dpbicpO1xyXG4gICAgICByb3V0ZXIucmVwbGFjZSgnL2F1dGgnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgfSwgW10pOyAvLyBSZW1vdmUgZmV0Y2hBbmRQZXJzaXN0V2FyZWhvdXNlSW5mbyBmcm9tIGRlcGVuZGVuY2llcyB0byBwcmV2ZW50IGluZmluaXRlIGxvb3BcclxuXHJcbiAgLy8gUGVyaW9kaWNhbGx5IHZhbGlkYXRlIHNlc3Npb24gYW5kIHJlZnJlc2ggdG9rZW4gaWYgbmVlZGVkXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghdG9rZW4gfHwgIXJlZnJlc2hUb2tlbikge1xyXG4gICAgICBjb25zb2xlLmxvZygnTm8gdG9rZW4gb3IgcmVmcmVzaCB0b2tlbiBhdmFpbGFibGUsIHNraXBwaW5nIHBlcmlvZGljIHZhbGlkYXRpb24nKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnUGVyaW9kaWMgdG9rZW4gdmFsaWRhdGlvbiAtIGNoZWNraW5nIHRva2VuIHZhbGlkaXR5Jyk7XHJcbiAgICAgICAgLy8gVHJ5IHRvIHZhbGlkYXRlIGN1cnJlbnQgdG9rZW5cclxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3ZhbGlkYXRlJywge1xyXG4gICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGlmICghcmVzLm9rKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gdmFsaWRhdGlvbiBmYWlsZWQsIGF0dGVtcHRpbmcgcmVmcmVzaCcpO1xyXG4gICAgICAgICAgLy8gVG9rZW4gaXMgaW52YWxpZCwgdHJ5IHRvIHJlZnJlc2hcclxuICAgICAgICAgIGNvbnN0IHJlZnJlc2hTdWNjZXNzID0gYXdhaXQgcmVmcmVzaEFjY2Vzc1Rva2VuKCk7XHJcbiAgICAgICAgICBpZiAoIXJlZnJlc2hTdWNjZXNzKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiByZWZyZXNoIGZhaWxlZCwgbG9nZ2luZyBvdXQgdXNlcicpO1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb190b2tlbicpO1xyXG4gICAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb19yZWZyZXNoX3Rva2VuJyk7XHJcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdkaWRvX3VzZXInKTtcclxuICAgICAgICAgICAgcm91dGVyLnJlcGxhY2UoXCIvYXV0aFwiKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiByZWZyZXNoIHN1Y2Nlc3NmdWwnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1Rva2VuIHZhbGlkYXRpb24gc3VjY2Vzc2Z1bCcpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnTmV0d29yayBlcnJvciBkdXJpbmcgdG9rZW4gdmFsaWRhdGlvbiwgYXR0ZW1wdGluZyByZWZyZXNoJyk7XHJcbiAgICAgICAgLy8gTmV0d29yayBlcnJvciwgdHJ5IHRvIHJlZnJlc2ggdG9rZW5cclxuICAgICAgICBjb25zdCByZWZyZXNoU3VjY2VzcyA9IGF3YWl0IHJlZnJlc2hBY2Nlc3NUb2tlbigpO1xyXG4gICAgICAgIGlmICghcmVmcmVzaFN1Y2Nlc3MpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdUb2tlbiByZWZyZXNoIGZhaWxlZCBhZnRlciBuZXR3b3JrIGVycm9yLCBsb2dnaW5nIG91dCB1c2VyJyk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb190b2tlbicpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2RpZG9fcmVmcmVzaF90b2tlbicpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2RpZG9fdXNlcicpO1xyXG4gICAgICAgICAgcm91dGVyLnJlcGxhY2UoXCIvYXV0aFwiKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1Rva2VuIHJlZnJlc2ggc3VjY2Vzc2Z1bCBhZnRlciBuZXR3b3JrIGVycm9yJyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9LCAzMCAqIDYwICogMTAwMCk7IC8vIDMwIG1pbnV0ZXNcclxuICAgIFxyXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xyXG4gIH0sIFt0b2tlbiwgcmVmcmVzaFRva2VuLCByZWZyZXNoQWNjZXNzVG9rZW4sIHJvdXRlcl0pO1xyXG5cclxuICBjb25zdCBsb2dpbiA9ICh1c2VyOiBVc2VyLCB0b2tlbjogc3RyaW5nLCByZWZyZXNoVG9rZW5WYWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQgbG9naW4gY2FsbGVkIHdpdGg6Jywge1xyXG4gICAgICB1c2VyVXVpZDogdXNlci51dWlkLFxyXG4gICAgICB1c2VyRW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgIHVzZXJXYXJlaG91c2VVdWlkOiB1c2VyLndhcmVob3VzZVV1aWRTdHJpbmcgfHwgdXNlci53YXJlaG91c2VVdWlkLFxyXG4gICAgICB1c2VyV2FyZWhvdXNlVXVpZFN0cmluZzogdXNlci53YXJlaG91c2VVdWlkU3RyaW5nLFxyXG4gICAgICB1c2VyV2FyZWhvdXNlVXVpZExlZ2FjeTogdXNlci53YXJlaG91c2VVdWlkLFxyXG4gICAgICB1c2VyV2FyZWhvdXNlTmFtZTogdXNlci53YXJlaG91c2VOYW1lLFxyXG4gICAgICBoYXNUb2tlbjogISF0b2tlbixcclxuICAgICAgaGFzUmVmcmVzaFRva2VuOiAhIXJlZnJlc2hUb2tlblZhbHVlXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgc2V0VXNlcih1c2VyKTtcclxuICAgIHNldFRva2VuKHRva2VuKTtcclxuICAgIHNldFJlZnJlc2hUb2tlbihyZWZyZXNoVG9rZW5WYWx1ZSk7XHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImRpZG9fdXNlclwiLCBKU09OLnN0cmluZ2lmeSh1c2VyKSk7XHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImRpZG9fdG9rZW5cIiwgdG9rZW4pO1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJkaWRvX3JlZnJlc2hfdG9rZW5cIiwgcmVmcmVzaFRva2VuVmFsdWUpO1xyXG4gICAgXHJcbiAgICAvLyBBbHdheXMgdHJpZ2dlciBiYWNrZ3JvdW5kIGZldGNoIHRvIGVuc3VyZSB3ZSBoYXZlIHRoZSBsYXRlc3Qgd2FyZWhvdXNlIGluZm9cclxuICAgIC8vIFRoaXMgaXMgZXNwZWNpYWxseSBpbXBvcnRhbnQgYWZ0ZXIgZGF0YWJhc2UgY2xlYXJpbmcgd2hlbiB3YXJlaG91c2UgVVVJRHMgbWF5IGhhdmUgY2hhbmdlZFxyXG4gICAgY29uc29sZS5sb2coJ1RyaWdnZXJpbmcgYmFja2dyb3VuZCB3YXJlaG91c2UgaW5mbyBmZXRjaCB0byBlbnN1cmUgbGF0ZXN0IGRhdGEnKTtcclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBmZXRjaEFuZFBlcnNpc3RXYXJlaG91c2VJbmZvKHVzZXIsIHRva2VuKTtcclxuICAgIH0sIDApO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dCBsb2dvdXQgY2FsbGVkIC0gY2xlYXJpbmcgc2Vzc2lvbiBhbmQgcmVkaXJlY3RpbmcgdG8gL2F1dGgnKTtcclxuICAgIFxyXG4gICAgLy8gUmV2b2tlIHRva2VucyBvbiBiYWNrZW5kIGlmIHdlIGhhdmUgdGhlbVxyXG4gICAgaWYgKHRva2VuICYmIHJlZnJlc2hUb2tlbikge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0Jywge1xyXG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICByZWZyZXNoVG9rZW4sXHJcbiAgICAgICAgICAgIHJldm9rZUFsbDogZmFsc2UsIC8vIE9ubHkgcmV2b2tlIGN1cnJlbnQgc2Vzc2lvblxyXG4gICAgICAgICAgfSksXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZHVyaW5nIGxvZ291dDonLCBlcnJvcik7XHJcbiAgICAgICAgLy8gQ29udGludWUgd2l0aCBsb2dvdXQgZXZlbiBpZiBiYWNrZW5kIGNhbGwgZmFpbHNcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICBzZXRVc2VyKG51bGwpO1xyXG4gICAgc2V0VG9rZW4obnVsbCk7XHJcbiAgICBzZXRSZWZyZXNoVG9rZW4obnVsbCk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZGlkb190b2tlbicpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2RpZG9fcmVmcmVzaF90b2tlbicpO1xyXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2RpZG9fdXNlcicpO1xyXG4gICAgXHJcbiAgICBjb25zb2xlLmxvZygnU2Vzc2lvbiBjbGVhcmVkLCByZWRpcmVjdGluZyB0byAvYXV0aCcpO1xyXG4gICAgcm91dGVyLnJlcGxhY2UoXCIvYXV0aFwiKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBjaGVja1VzZXJFeGlzdHMgPSB1c2VDYWxsYmFjayhhc3luYyAodXVpZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XHJcbiAgICBpZiAoIXRva2VuKSByZXR1cm4gZmFsc2U7XHJcbiAgICBcclxuICAgIC8vIFNhZmV0eSBjaGVjazogZW5zdXJlIFVVSUQgaXMgdmFsaWRcclxuICAgIGlmICghdXVpZCB8fCB0eXBlb2YgdXVpZCAhPT0gJ3N0cmluZycgfHwgdXVpZC50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdjaGVja1VzZXJFeGlzdHM6IGludmFsaWQgVVVJRCBwcm92aWRlZDonLCB1dWlkKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3VzZXJzLyR7dXVpZH1gLCB7XHJcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwNCkgcmV0dXJuIGZhbHNlO1xyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjaGVjayB1c2VyIGV4aXN0ZW5jZScpO1xyXG4gICAgICBcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjaGVja2luZyB1c2VyIGV4aXN0ZW5jZTonLCBlcnJvcik7XHJcbiAgICAgIHJldHVybiBmYWxzZTtcclxuICAgIH1cclxuICB9LCBbdG9rZW5dKTtcclxuXHJcbiAgY29uc3Qgc3dpdGNoV2FyZWhvdXNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKHdhcmVob3VzZVV1aWQ6IHN0cmluZywgd2FyZWhvdXNlTmFtZTogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAoIXVzZXI/LnV1aWQgfHwgIXRva2VuKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBTYWZldHkgY2hlY2s6IGVuc3VyZSB1c2VyIFVVSUQgaXMgdmFsaWRcclxuICAgIGlmICh0eXBlb2YgdXNlci51dWlkICE9PSAnc3RyaW5nJyB8fCB1c2VyLnV1aWQudHJpbSgpID09PSAnJykge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgdXNlciBVVUlEJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gVXBkYXRlIHVzZXIncyB3YXJlaG91c2Ugb24gYmFja2VuZFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3VzZXJzLyR7dXNlci51dWlkfS93YXJlaG91c2VgLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgd2FyZWhvdXNlVXVpZCB9KSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHdhcmVob3VzZSBvbiBiYWNrZW5kJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB1c2VyIGNvbnRleHQgd2l0aCBuZXcgd2FyZWhvdXNlIGluZm9cclxuICAgICAgY29uc3QgdXBkYXRlZFVzZXI6IFVzZXIgPSB7XHJcbiAgICAgICAgLi4udXNlcixcclxuICAgICAgICB3YXJlaG91c2VVdWlkOiB3YXJlaG91c2VVdWlkLFxyXG4gICAgICAgIHdhcmVob3VzZU5hbWU6IHdhcmVob3VzZU5hbWUsXHJcbiAgICAgIH07XHJcblxyXG4gICAgICAvLyBVcGRhdGUgbG9jYWxTdG9yYWdlXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZGlkb191c2VyXCIsIEpTT04uc3RyaW5naWZ5KHVwZGF0ZWRVc2VyKSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBVcGRhdGUgY29udGV4dCBzdGF0ZVxyXG4gICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcclxuICAgICAgXHJcbiAgICAgIGNvbnNvbGUubG9nKCdXYXJlaG91c2Ugc3dpdGNoZWQgc3VjY2Vzc2Z1bGx5OicsIHsgd2FyZWhvdXNlVXVpZCwgd2FyZWhvdXNlTmFtZSB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN3aXRjaGluZyB3YXJlaG91c2U6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9LCBbdXNlciwgdG9rZW5dKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlclxyXG4gICAgICB2YWx1ZT17e1xyXG4gICAgICAgIHVzZXIsXHJcbiAgICAgICAgdG9rZW4sXHJcbiAgICAgICAgcmVmcmVzaFRva2VuLFxyXG4gICAgICAgIGxvYWRpbmcsXHJcbiAgICAgICAgbG9naW4sXHJcbiAgICAgICAgbG9nb3V0LFxyXG4gICAgICAgIHJlZnJlc2hBY2Nlc3NUb2tlbixcclxuICAgICAgICBnZXRTZWN1cml0eVN0YXR1cyxcclxuICAgICAgICBmZXRjaEFuZFBlcnNpc3RXYXJlaG91c2VJbmZvLFxyXG4gICAgICAgIGNoZWNrVXNlckV4aXN0cyxcclxuICAgICAgICBzd2l0Y2hXYXJlaG91c2UsXHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xyXG4gIGlmICghY29udGV4dCkge1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKFwidXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlclwiKTtcclxuICB9XHJcbiAgcmV0dXJuIGNvbnRleHQ7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwidXNlUm91dGVyIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwidG9rZW4iLCJzZXRUb2tlbiIsInJlZnJlc2hUb2tlbiIsInNldFJlZnJlc2hUb2tlbiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZmV0Y2hpbmdXYXJlaG91c2UiLCJzZXRGZXRjaGluZ1dhcmVob3VzZSIsInJlZnJlc2hpbmdUb2tlbiIsInNldFJlZnJlc2hpbmdUb2tlbiIsInJvdXRlciIsImZldGNoaW5nV2FyZWhvdXNlUmVmIiwidG9rZW5SZWYiLCJ1c2VyUmVmIiwiY3VycmVudCIsImZldGNoQW5kUGVyc2lzdFdhcmVob3VzZUluZm8iLCJ1c2VyT2JqUGFyYW0iLCJ0b2tlblBhcmFtIiwidG9rZW5Ub1VzZSIsInVzZXJUb0ZldGNoIiwiY29uc29sZSIsImxvZyIsInV1aWQiLCJ1c2VyVG9GZXRjaFdhcmVob3VzZVV1aWQiLCJ3YXJlaG91c2VVdWlkU3RyaW5nIiwid2FyZWhvdXNlVXVpZCIsImN1cnJlbnRVc2VyIiwiY3VycmVudFVzZXJXYXJlaG91c2VVdWlkIiwibm9Ub2tlbiIsIm5vVXNlclV1aWQiLCJhbHJlYWR5RmV0Y2hpbmciLCJ0cmltIiwic3RvcmVkVG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlclJlcyIsImZldGNoIiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJvayIsImVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImxhdGVzdFVzZXIiLCJqc29uIiwiZmluYWxXYXJlaG91c2VVdWlkIiwidXBkYXRlZFVzZXIiLCJ3YXJlaG91c2VOYW1lIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJ3YXJlaG91c2VSZXMiLCJ3YXJlaG91c2UiLCJuYW1lIiwiZXJyIiwiZmFsbGJhY2tFcnIiLCJyZWZyZXNoQWNjZXNzVG9rZW4iLCJyZXNwb25zZSIsIm1ldGhvZCIsImJvZHkiLCJjbGllbnRJcCIsInVzZXJBZ2VudCIsIm5hdmlnYXRvciIsImRhdGEiLCJhY2Nlc3NUb2tlbiIsImdldFNlY3VyaXR5U3RhdHVzIiwic3RvcmVkVXNlciIsInN0b3JlZFJlZnJlc2hUb2tlbiIsImhhc1N0b3JlZFVzZXIiLCJoYXNTdG9yZWRUb2tlbiIsImhhc1N0b3JlZFJlZnJlc2hUb2tlbiIsInVzZXJPYmoiLCJwYXJzZSIsInVzZXJVdWlkIiwidXNlckVtYWlsIiwiZW1haWwiLCJ1c2VyV2FyZWhvdXNlVXVpZCIsInVzZXJXYXJlaG91c2VOYW1lIiwidmFsaWRhdGVVc2VyRXhpc3RzIiwicmVtb3ZlSXRlbSIsInJlcGxhY2UiLCJzZXRUaW1lb3V0Iiwid2luZG93IiwibG9jYXRpb24iLCJwYXRobmFtZSIsImluY2x1ZGVzIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInJlcyIsInJlZnJlc2hTdWNjZXNzIiwiY2xlYXJJbnRlcnZhbCIsImxvZ2luIiwicmVmcmVzaFRva2VuVmFsdWUiLCJ1c2VyV2FyZWhvdXNlVXVpZFN0cmluZyIsInVzZXJXYXJlaG91c2VVdWlkTGVnYWN5IiwiaGFzVG9rZW4iLCJoYXNSZWZyZXNoVG9rZW4iLCJsb2dvdXQiLCJyZXZva2VBbGwiLCJjaGVja1VzZXJFeGlzdHMiLCJFcnJvciIsInN3aXRjaFdhcmVob3VzZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VBdXRoIiwiY29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/themes */ \"(ssr)/./styles/themes.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ThemeProvider = ({ children, initialTheme = \"white\" })=>{\n    const [themeName, setThemeName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(initialTheme));\n    // Load saved theme from localStorage on initial render (client-side only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    // Update CSS variables when theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run on client side\n        if (true) return;\n        const root = document.documentElement;\n        // Set color variables\n        Object.entries(theme.colors).forEach(([key, value])=>{\n            root.style.setProperty(`--color-${key}`, value);\n        });\n        // Set shadow variables\n        Object.entries(theme.shadows).forEach(([key, value])=>{\n            root.style.setProperty(`--shadow-${key}`, value);\n        });\n        // Set border radius variables\n        Object.entries(theme.borderRadius).forEach(([key, value])=>{\n            root.style.setProperty(`--radius-${key}`, value);\n        });\n        // Update the data-theme attribute for potential CSS selectors\n        root.setAttribute(\"data-theme\", theme.name);\n    }, [\n        theme\n    ]);\n    const changeTheme = (newThemeName)=>{\n        const newTheme = (0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(newThemeName);\n        setThemeName(newTheme.name);\n        setTheme(newTheme);\n        // Save to localStorage\n        localStorage.setItem(\"theme\", newTheme.name);\n    };\n    const availableThemes = _styles_themes__WEBPACK_IMPORTED_MODULE_2__.allThemes.map((t)=>({\n            name: t.name,\n            label: t.name.charAt(0).toUpperCase() + t.name.slice(1)\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            themeName,\n            setTheme: changeTheme,\n            availableThemes\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0cy9UaGVtZUNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlGO0FBQ1I7QUFTakYsTUFBTU8sNkJBQWVOLG9EQUFhQSxDQUErQk87QUFPMUQsTUFBTUMsZ0JBQThDLENBQUMsRUFDMURDLFFBQVEsRUFDUkMsZUFBZSxPQUFPLEVBQ3ZCO0lBQ0MsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdWLCtDQUFRQSxDQUFTUTtJQUNuRCxNQUFNLENBQUNHLE9BQU9DLFNBQVMsR0FBR1osK0NBQVFBLENBQVFFLDhEQUFjQSxDQUFDTTtJQUV6RCwwRUFBMEU7SUFDMUVQLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxLQUFrQixFQUFhLEVBT2xDO0lBQ0gsR0FBRyxFQUFFO0lBRUwsMENBQTBDO0lBQzFDQSxnREFBU0EsQ0FBQztRQUNSLDBCQUEwQjtRQUMxQixJQUFJLElBQWtCLEVBQWE7UUFFbkMsTUFBTWlCLE9BQU9DLFNBQVNDLGVBQWU7UUFFckMsc0JBQXNCO1FBQ3RCQyxPQUFPQyxPQUFPLENBQUNYLE1BQU1ZLE1BQU0sRUFBRUMsT0FBTyxDQUFDLENBQUMsQ0FBQ0MsS0FBS0MsTUFBTTtZQUNoRFIsS0FBS1MsS0FBSyxDQUFDQyxXQUFXLENBQUMsQ0FBQyxRQUFRLEVBQUVILElBQUksQ0FBQyxFQUFFQztRQUMzQztRQUVBLHVCQUF1QjtRQUN2QkwsT0FBT0MsT0FBTyxDQUFDWCxNQUFNa0IsT0FBTyxFQUFFTCxPQUFPLENBQUMsQ0FBQyxDQUFDQyxLQUFLQyxNQUFNO1lBQ2pEUixLQUFLUyxLQUFLLENBQUNDLFdBQVcsQ0FBQyxDQUFDLFNBQVMsRUFBRUgsSUFBSSxDQUFDLEVBQUVDO1FBQzVDO1FBRUEsOEJBQThCO1FBQzlCTCxPQUFPQyxPQUFPLENBQUNYLE1BQU1tQixZQUFZLEVBQUVOLE9BQU8sQ0FBQyxDQUFDLENBQUNDLEtBQUtDLE1BQU07WUFDdERSLEtBQUtTLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLENBQUMsU0FBUyxFQUFFSCxJQUFJLENBQUMsRUFBRUM7UUFDNUM7UUFFQSw4REFBOEQ7UUFDOURSLEtBQUthLFlBQVksQ0FBQyxjQUFjcEIsTUFBTU0sSUFBSTtJQUM1QyxHQUFHO1FBQUNOO0tBQU07SUFFVixNQUFNcUIsY0FBYyxDQUFDQztRQUNuQixNQUFNQyxXQUFXaEMsOERBQWNBLENBQUMrQjtRQUNoQ3ZCLGFBQWF3QixTQUFTakIsSUFBSTtRQUMxQkwsU0FBU3NCO1FBQ1QsdUJBQXVCO1FBQ3ZCcEIsYUFBYXFCLE9BQU8sQ0FBQyxTQUFTRCxTQUFTakIsSUFBSTtJQUM3QztJQUVBLE1BQU1tQixrQkFBa0JqQyxxREFBU0EsQ0FBQ2tDLEdBQUcsQ0FBQyxDQUFDQyxJQUFPO1lBQzVDckIsTUFBTXFCLEVBQUVyQixJQUFJO1lBQ1pzQixPQUFPRCxFQUFFckIsSUFBSSxDQUFDdUIsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0gsRUFBRXJCLElBQUksQ0FBQ3lCLEtBQUssQ0FBQztRQUN2RDtJQUVBLHFCQUNFLDhEQUFDdEMsYUFBYXVDLFFBQVE7UUFDcEJqQixPQUFPO1lBQ0xmO1lBQ0FGO1lBQ0FHLFVBQVVvQjtZQUNWSTtRQUNGO2tCQUVDN0I7Ozs7OztBQUdQLEVBQUU7QUFFSyxNQUFNcUMsV0FBVztJQUN0QixNQUFNQyxVQUFVOUMsaURBQVVBLENBQUNLO0lBQzNCLElBQUl5QyxZQUFZeEMsV0FBVztRQUN6QixNQUFNLElBQUl5QyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvLi9jb250ZXh0cy9UaGVtZUNvbnRleHQudHN4PzkyNTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIFJlYWN0Tm9kZSwgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgVGhlbWUsIGRlZmF1bHRUaGVtZSwgZ2V0VGhlbWVCeU5hbWUsIGFsbFRoZW1lcyB9IGZyb20gJ0Avc3R5bGVzL3RoZW1lcyc7XHJcblxyXG50eXBlIFRoZW1lQ29udGV4dFR5cGUgPSB7XHJcbiAgdGhlbWU6IFRoZW1lO1xyXG4gIHRoZW1lTmFtZTogc3RyaW5nO1xyXG4gIHNldFRoZW1lOiAodGhlbWVOYW1lOiBzdHJpbmcpID0+IHZvaWQ7XHJcbiAgYXZhaWxhYmxlVGhlbWVzOiB7IG5hbWU6IHN0cmluZzsgbGFiZWw6IHN0cmluZyB9W107XHJcbn07XHJcblxyXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XHJcblxyXG50eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyA9IHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xyXG4gIGluaXRpYWxUaGVtZT86IHN0cmluZztcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBUaGVtZVByb3ZpZGVyOiBSZWFjdC5GQzxUaGVtZVByb3ZpZGVyUHJvcHM+ID0gKHtcclxuICBjaGlsZHJlbixcclxuICBpbml0aWFsVGhlbWUgPSAnd2hpdGUnLFxyXG59KSA9PiB7XHJcbiAgY29uc3QgW3RoZW1lTmFtZSwgc2V0VGhlbWVOYW1lXSA9IHVzZVN0YXRlPHN0cmluZz4oaW5pdGlhbFRoZW1lKTtcclxuICBjb25zdCBbdGhlbWUsIHNldFRoZW1lXSA9IHVzZVN0YXRlPFRoZW1lPihnZXRUaGVtZUJ5TmFtZShpbml0aWFsVGhlbWUpKTtcclxuXHJcbiAgLy8gTG9hZCBzYXZlZCB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBpbml0aWFsIHJlbmRlciAoY2xpZW50LXNpZGUgb25seSlcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKTtcclxuICAgICAgaWYgKHNhdmVkVGhlbWUpIHtcclxuICAgICAgICBjb25zdCBzYXZlZFRoZW1lT2JqID0gZ2V0VGhlbWVCeU5hbWUoc2F2ZWRUaGVtZSk7XHJcbiAgICAgICAgc2V0VGhlbWVOYW1lKHNhdmVkVGhlbWVPYmoubmFtZSk7XHJcbiAgICAgICAgc2V0VGhlbWUoc2F2ZWRUaGVtZU9iaik7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIFVwZGF0ZSBDU1MgdmFyaWFibGVzIHdoZW4gdGhlbWUgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBPbmx5IHJ1biBvbiBjbGllbnQgc2lkZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm47XHJcbiAgICBcclxuICAgIGNvbnN0IHJvb3QgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7XHJcbiAgICBcclxuICAgIC8vIFNldCBjb2xvciB2YXJpYWJsZXNcclxuICAgIE9iamVjdC5lbnRyaWVzKHRoZW1lLmNvbG9ycykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XHJcbiAgICAgIHJvb3Quc3R5bGUuc2V0UHJvcGVydHkoYC0tY29sb3ItJHtrZXl9YCwgdmFsdWUpO1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gU2V0IHNoYWRvdyB2YXJpYWJsZXNcclxuICAgIE9iamVjdC5lbnRyaWVzKHRoZW1lLnNoYWRvd3MpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xyXG4gICAgICByb290LnN0eWxlLnNldFByb3BlcnR5KGAtLXNoYWRvdy0ke2tleX1gLCB2YWx1ZSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBTZXQgYm9yZGVyIHJhZGl1cyB2YXJpYWJsZXNcclxuICAgIE9iamVjdC5lbnRyaWVzKHRoZW1lLmJvcmRlclJhZGl1cykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XHJcbiAgICAgIHJvb3Quc3R5bGUuc2V0UHJvcGVydHkoYC0tcmFkaXVzLSR7a2V5fWAsIHZhbHVlKTtcclxuICAgIH0pO1xyXG4gICAgXHJcbiAgICAvLyBVcGRhdGUgdGhlIGRhdGEtdGhlbWUgYXR0cmlidXRlIGZvciBwb3RlbnRpYWwgQ1NTIHNlbGVjdG9yc1xyXG4gICAgcm9vdC5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCB0aGVtZS5uYW1lKTtcclxuICB9LCBbdGhlbWVdKTtcclxuXHJcbiAgY29uc3QgY2hhbmdlVGhlbWUgPSAobmV3VGhlbWVOYW1lOiBzdHJpbmcpID0+IHtcclxuICAgIGNvbnN0IG5ld1RoZW1lID0gZ2V0VGhlbWVCeU5hbWUobmV3VGhlbWVOYW1lKTtcclxuICAgIHNldFRoZW1lTmFtZShuZXdUaGVtZS5uYW1lKTtcclxuICAgIHNldFRoZW1lKG5ld1RoZW1lKTtcclxuICAgIC8vIFNhdmUgdG8gbG9jYWxTdG9yYWdlXHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndGhlbWUnLCBuZXdUaGVtZS5uYW1lKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBhdmFpbGFibGVUaGVtZXMgPSBhbGxUaGVtZXMubWFwKCh0KSA9PiAoe1xyXG4gICAgbmFtZTogdC5uYW1lLFxyXG4gICAgbGFiZWw6IHQubmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHQubmFtZS5zbGljZSgxKSxcclxuICB9KSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8VGhlbWVDb250ZXh0LlByb3ZpZGVyXHJcbiAgICAgIHZhbHVlPXt7XHJcbiAgICAgICAgdGhlbWUsXHJcbiAgICAgICAgdGhlbWVOYW1lLFxyXG4gICAgICAgIHNldFRoZW1lOiBjaGFuZ2VUaGVtZSxcclxuICAgICAgICBhdmFpbGFibGVUaGVtZXMsXHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgdXNlVGhlbWUgPSAoKTogVGhlbWVDb250ZXh0VHlwZSA9PiB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KTtcclxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRoZW1lIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUaGVtZVByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiZ2V0VGhlbWVCeU5hbWUiLCJhbGxUaGVtZXMiLCJUaGVtZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJpbml0aWFsVGhlbWUiLCJ0aGVtZU5hbWUiLCJzZXRUaGVtZU5hbWUiLCJ0aGVtZSIsInNldFRoZW1lIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzYXZlZFRoZW1lT2JqIiwibmFtZSIsInJvb3QiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsIk9iamVjdCIsImVudHJpZXMiLCJjb2xvcnMiLCJmb3JFYWNoIiwia2V5IiwidmFsdWUiLCJzdHlsZSIsInNldFByb3BlcnR5Iiwic2hhZG93cyIsImJvcmRlclJhZGl1cyIsInNldEF0dHJpYnV0ZSIsImNoYW5nZVRoZW1lIiwibmV3VGhlbWVOYW1lIiwibmV3VGhlbWUiLCJzZXRJdGVtIiwiYXZhaWxhYmxlVGhlbWVzIiwibWFwIiwidCIsImxhYmVsIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsIlByb3ZpZGVyIiwidXNlVGhlbWUiLCJjb250ZXh0IiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./styles/themes.ts":
/*!**************************!*\
  !*** ./styles/themes.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allThemes: () => (/* binding */ allThemes),\n/* harmony export */   blueTheme: () => (/* binding */ blueTheme),\n/* harmony export */   darkTheme: () => (/* binding */ darkTheme),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   getThemeByName: () => (/* binding */ getThemeByName),\n/* harmony export */   lightTheme: () => (/* binding */ lightTheme)\n/* harmony export */ });\nconst lightTheme = {\n    name: \"light\",\n    colors: {\n        primary: \"#2563eb\",\n        primaryLight: \"#3b82f6\",\n        primaryDark: \"#1d4ed8\",\n        background: \"#f9fafb\",\n        surface: \"#ffffff\",\n        textPrimary: \"#111827\",\n        textSecondary: \"#4b5563\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#e5e7eb\",\n        hover: \"#f3f4f6\",\n        active: \"#e5e7eb\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst darkTheme = {\n    name: \"dark\",\n    colors: {\n        primary: \"#3b82f6\",\n        primaryLight: \"#60a5fa\",\n        primaryDark: \"#2563eb\",\n        background: \"#111827\",\n        surface: \"#1f2937\",\n        textPrimary: \"#f9fafb\",\n        textSecondary: \"#d1d5db\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#374151\",\n        hover: \"#1f2937\",\n        active: \"#374151\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.25)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst blueTheme = {\n    name: \"blue\",\n    colors: {\n        primary: \"#1d4ed8\",\n        primaryLight: \"#2563eb\",\n        primaryDark: \"#1e40af\",\n        background: \"#eff6ff\",\n        surface: \"#ffffff\",\n        textPrimary: \"#1e3a8a\",\n        textSecondary: \"#1e40af\",\n        success: \"#047857\",\n        warning: \"#b45309\",\n        error: \"#b91c1c\",\n        info: \"#1d4ed8\",\n        border: \"#bfdbfe\",\n        hover: \"#dbeafe\",\n        active: \"#bfdbfe\"\n    },\n    shadows: {\n        small: \"0 1px 3px 0 rgb(29 78 216 / 0.1), 0 1px 2px -1px rgb(29 78 216 / 0.1)\",\n        medium: \"0 4px 6px -1px rgb(29 78 216 / 0.1), 0 2px 4px -2px rgb(29 78 216 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(29 78 216 / 0.1), 0 4px 6px -4px rgb(29 78 216 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\n// Export a default theme (can be changed based on user preference)\nconst defaultTheme = lightTheme;\n// Export all themes as an array\nconst allThemes = [\n    lightTheme,\n    darkTheme,\n    blueTheme\n];\n// Helper function to get a theme by name\nconst getThemeByName = (name)=>{\n    return allThemes.find((theme)=>theme.name === name) || defaultTheme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./styles/themes.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f63b265f9b07\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kaWRvLWRpc3RyaWJ1dGlvbi1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz8xZDIzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjYzYjI2NWY5YjA3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/callback/page.tsx":
/*!************************************!*\
  !*** ./app/auth/callback/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\auth\callback\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Dido Distribution\",\n    description: \"Distribution management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        richColors: true,\n                        closeButton: true,\n                        duration: 4000,\n                        position: \"top-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmlCO0FBQ2lCO0FBQ1A7QUFJMUIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1gsMkpBQWU7c0JBQzlCLDRFQUFDQyxpREFBU0E7O29CQUNQTTtrQ0FDRCw4REFBQ0wsMkNBQU9BO3dCQUFDVSxVQUFVO3dCQUFDQyxXQUFXO3dCQUFDQyxVQUFVO3dCQUFNQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS25FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnLi9wcm92aWRlcnMnO1xyXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJztcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdEaWRvIERpc3RyaWJ1dGlvbicsXHJcbiAgZGVzY3JpcHRpb246ICdEaXN0cmlidXRpb24gbWFuYWdlbWVudCBzeXN0ZW0nLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XHJcbiAgICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgIDxUb2FzdGVyIHJpY2hDb2xvcnMgY2xvc2VCdXR0b24gZHVyYXRpb249ezQwMDB9IHBvc2l0aW9uPVwidG9wLWNlbnRlclwiIC8+XHJcbiAgICAgICAgPC9Qcm92aWRlcnM+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInJpY2hDb2xvcnMiLCJjbG9zZUJ1dHRvbiIsImR1cmF0aW9uIiwicG9zaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@tanstack","vendor-chunks/sonner"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fcallback%2Fpage&page=%2Fauth%2Fcallback%2Fpage&appPaths=%2Fauth%2Fcallback%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fcallback%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();